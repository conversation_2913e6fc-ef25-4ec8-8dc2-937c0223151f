<Window x:Class="AWSAutoRegister.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="AWS Auto Register - Dual Browser Mode"
        Height="608" Width="650"
        MinHeight="608" MinWidth="550"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        Icon="favicon.ico"
        UseLayoutRounding="True"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType"
        SnapsToDevicePixels="True"
        Background="#F5F7FA"
        Closing="MainWindow_Closing">

    <Window.Resources>
        <!-- 自定义滚动条样式 -->
        <Style x:Key="CustomScrollBarStyle" TargetType="ScrollBar">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Width" Value="8"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ScrollBar">
                        <Grid>
                            <Border Background="Transparent" Width="8"/>
                            <Track Name="PART_Track" IsDirectionReversed="True" Width="6" HorizontalAlignment="Center">
                                <Track.Thumb>
                                    <Thumb>
                                        <Thumb.Template>
                                            <ControlTemplate TargetType="Thumb">
                                                <Border Background="#C1C7CD"
                                                        CornerRadius="3"
                                                        Opacity="0.6">
                                                    <Border.Style>
                                                        <Style TargetType="Border">
                                                            <Style.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="Opacity" Value="0.8"/>
                                                                    <Setter Property="Background" Value="#A0AEC0"/>
                                                                </Trigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Border.Style>
                                                </Border>
                                            </ControlTemplate>
                                        </Thumb.Template>
                                    </Thumb>
                                </Track.Thumb>
                            </Track>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 自定义ScrollViewer样式 -->
        <Style x:Key="CustomScrollViewerStyle" TargetType="ScrollViewer">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ScrollViewer">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <ScrollContentPresenter Grid.Column="0" Grid.Row="0"
                                                    Content="{TemplateBinding Content}"
                                                    ContentTemplate="{TemplateBinding ContentTemplate}"
                                                    CanContentScroll="{TemplateBinding CanContentScroll}"/>
                            <ScrollBar Grid.Column="1" Grid.Row="0"
                                       Name="PART_VerticalScrollBar"
                                       Style="{StaticResource CustomScrollBarStyle}"
                                       Value="{TemplateBinding VerticalOffset}"
                                       Maximum="{TemplateBinding ScrollableHeight}"
                                       ViewportSize="{TemplateBinding ViewportHeight}"
                                       Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                       Margin="2,0,0,0"/>
                            <ScrollBar Grid.Column="0" Grid.Row="1"
                                       Name="PART_HorizontalScrollBar"
                                       Orientation="Horizontal"
                                       Style="{StaticResource CustomScrollBarStyle}"
                                       Value="{TemplateBinding HorizontalOffset}"
                                       Maximum="{TemplateBinding ScrollableWidth}"
                                       ViewportSize="{TemplateBinding ViewportWidth}"
                                       Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                       Margin="0,2,0,0"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 现代化ComboBox样式 -->
        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E1E5E9"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Foreground" Value="#2D3748"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="IsEditable" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <ToggleButton Name="ToggleButton"
                                          Grid.Column="2"
                                          Focusable="False"
                                          IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                          ClickMode="Press">
                                <ToggleButton.Template>
                                    <ControlTemplate TargetType="ToggleButton">
                                        <Border Name="Border"
                                                Background="White"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="8">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <ContentPresenter Grid.Column="0"
                                                                  Name="ContentSite"
                                                                  IsHitTestVisible="False"
                                                                  Content="{Binding Path=SelectionBoxItem, RelativeSource={RelativeSource AncestorType=ComboBox}}"
                                                                  ContentTemplate="{Binding Path=SelectionBoxItemTemplate, RelativeSource={RelativeSource AncestorType=ComboBox}}"
                                                                  ContentTemplateSelector="{Binding Path=ItemTemplateSelector, RelativeSource={RelativeSource AncestorType=ComboBox}}"
                                                                  Margin="8,4"
                                                                  VerticalAlignment="Center"
                                                                  HorizontalAlignment="Left"/>

                                                <Path Grid.Column="1"
                                                      Name="Arrow"
                                                      Fill="#718096"
                                                      HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"
                                                      Data="M 0 0 L 4 4 L 8 0 Z"
                                                      Margin="0,0,12,0"/>
                                            </Grid>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter TargetName="Border" Property="BorderBrush" Value="#4299E1"/>
                                                <Setter TargetName="Arrow" Property="Fill" Value="#4299E1"/>
                                            </Trigger>
                                            <Trigger Property="IsChecked" Value="True">
                                                <Setter TargetName="Border" Property="BorderBrush" Value="#3182CE"/>
                                                <Setter TargetName="Arrow" Property="Fill" Value="#3182CE"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </ToggleButton.Template>
                            </ToggleButton>

                            <Popup Name="Popup"
                                   Placement="Bottom"
                                   IsOpen="{TemplateBinding IsDropDownOpen}"
                                   AllowsTransparency="True"
                                   Focusable="False"
                                   PopupAnimation="Slide">
                                <Grid Name="DropDown"
                                      SnapsToDevicePixels="True"
                                      MinWidth="{TemplateBinding ActualWidth}"
                                      MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                    <Border Name="DropDownBorder"
                                            Background="White"
                                            BorderThickness="2"
                                            BorderBrush="#E1E5E9"
                                            CornerRadius="8"
                                            Margin="0,4,0,0">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#000000"
                                                              Opacity="0.1"
                                                              ShadowDepth="4"
                                                              BlurRadius="12"/>
                                        </Border.Effect>
                                        <ScrollViewer Margin="4,6"
                                                      SnapsToDevicePixels="True">
                                            <StackPanel IsItemsHost="True"
                                                        KeyboardNavigation.DirectionalNavigation="Contained"/>
                                        </ScrollViewer>
                                    </Border>
                                </Grid>
                            </Popup>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 现代化ComboBoxItem样式 -->
        <Style x:Key="ModernComboBoxItemStyle" TargetType="ComboBoxItem">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="2,1"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBoxItem">
                        <Border Name="Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsHighlighted" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#EBF8FF"/>
                                <Setter Property="Foreground" Value="#2B6CB0"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#3182CE"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4299E1"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Name="Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="#000000"
                                                  Opacity="0.1"
                                                  ShadowDepth="2"
                                                  BlurRadius="8"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#3182CE"/>
                                <Setter TargetName="Border" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#000000"
                                                          Opacity="0.15"
                                                          ShadowDepth="4"
                                                          BlurRadius="12"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#2C5282"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="Border" Property="Background" Value="#E2E8F0"/>
                                <Setter Property="Foreground" Value="#A0AEC0"/>
                                <Setter TargetName="Border" Property="Effect" Value="{x:Null}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="White"
          Margin="8"
          MaxWidth="820">
        <Grid.Effect>
            <DropShadowEffect Color="#000000"
                              Opacity="0.08"
                              ShadowDepth="8"
                              BlurRadius="24"/>
        </Grid.Effect>
        <Border Background="White"
                CornerRadius="12"
                Padding="12">
            <ScrollViewer VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Disabled"
                          PanningMode="VerticalOnly"
                          Style="{StaticResource CustomScrollViewerStyle}"
                          ScrollViewer.CanContentScroll="False">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*" MinHeight="200"/>
                    </Grid.RowDefinitions>

                    <!-- 文件选择区域 -->
                    <GroupBox Grid.Row="0" Header="📁 数据文件选择"
                              Padding="12" Margin="0,0,0,12"
                              FontSize="14" FontWeight="SemiBold"
                              Background="#FAFBFC"
                              BorderBrush="#E2E8F0"
                              BorderThickness="1">
                        <GroupBox.Effect>
                            <DropShadowEffect Color="#000000"
                                              Opacity="0.05"
                                              ShadowDepth="2"
                                              BlurRadius="8"/>
                        </GroupBox.Effect>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <Button Grid.Column="0"
                                    Name="SelectFileButton"
                                    Content="📁 选择文件"
                                    Margin="0,0,12,0"
                                    Height="32"
                                    Click="SelectFileButton_Click"
                                    Style="{StaticResource ModernButtonStyle}"
                                    Background="#4299E1"/>

                            <Border Grid.Column="1"
                                    Background="White"
                                    BorderBrush="#E2E8F0"
                                    BorderThickness="2"
                                    CornerRadius="8"
                                    Margin="0,0,12,0"
                                    Height="32">
                                <TextBox Name="FilePathTextBox"
                                         IsReadOnly="True"
                                         VerticalContentAlignment="Center"
                                         Padding="8,0"
                                         FontSize="12"
                                         Background="Transparent"
                                         BorderThickness="0"/>
                            </Border>

                            <Button Grid.Column="2"
                                    Name="LoadDataButton"
                                    Content="📊 加载信息"
                                    Height="32"
                                    Click="LoadDataButton_Click"
                                    IsEnabled="False"
                                    Style="{StaticResource ModernButtonStyle}"
                                    Background="#48BB78"/>
                        </Grid>
                    </GroupBox>

                    <!-- 配置区域 -->
                    <GroupBox Grid.Row="1" Header="🌐 浏览器与API配置"
                              Padding="12" Margin="0,0,0,12"
                              FontSize="14" FontWeight="SemiBold"
                              Background="#FAFBFC"
                              BorderBrush="#E2E8F0"
                              BorderThickness="1">
                        <GroupBox.Effect>
                            <DropShadowEffect Color="#000000"
                                              Opacity="0.05"
                                              ShadowDepth="2"
                                              BlurRadius="8"/>
                        </GroupBox.Effect>
                        <StackPanel>
                            <!-- 浏览器模式选择 -->
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0"
                                           Text="浏览器模式:"
                                           VerticalAlignment="Center"
                                           Margin="0,0,20,0"
                                           FontSize="14"
                                           FontWeight="Medium"
                                           Foreground="#2D3748"/>
                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <RadioButton Name="AdsPowerModeRadio"
                                                 Content="AdsPower"
                                                 Margin="0,0,20,0"
                                                 FontSize="13"
                                                 Checked="BrowserModeRadio_Checked"/>
                                    <RadioButton Name="LocalChromeNormalModeRadio"
                                                 Content="默认Chrome"
                                                 Margin="0,0,20,0"
                                                 FontSize="13"
                                                 Checked="BrowserModeRadio_Checked"/>
                                    <RadioButton Name="LocalChromeIncognitoModeRadio"
                                                 Content="无痕Chrome"
                                                 FontSize="13"
                                                 Checked="BrowserModeRadio_Checked"/>
                                </StackPanel>
                            </Grid>

                            <!-- 线程数量选择 -->
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0"
                                           Text="线程数量:"
                                           VerticalAlignment="Center"
                                           Margin="0,0,12,0"
                                           FontSize="14"
                                           FontWeight="Medium"
                                           Foreground="#2D3748"/>

                                <ComboBox Grid.Column="1"
                                          Name="ThreadCountComboBox"
                                          Style="{StaticResource ModernComboBoxStyle}"
                                          Width="120"
                                          Margin="0,0,12,0"
                                          SelectionChanged="ThreadCountComboBox_SelectionChanged"
                                          ItemContainerStyle="{StaticResource ModernComboBoxItemStyle}">
                                    <ComboBoxItem Content="单线程模式" Tag="1"/>
                                    <ComboBoxItem Content="2个线程" Tag="2"/>
                                    <ComboBoxItem Content="3个线程" Tag="3"/>
                                    <ComboBoxItem Content="4个线程" Tag="4"/>
                                    <ComboBoxItem Content="5个线程" Tag="5"/>
                                    <ComboBoxItem Content="6个线程" Tag="6"/>
                                </ComboBox>

                                <TextBlock Grid.Column="2"
                                           Name="ThreadModeHint"
                                           Text="💡 多线程模式需要使用默认Chrome或无痕Chrome"
                                           VerticalAlignment="Center"
                                           FontSize="11"
                                           Foreground="#718096"
                                           Visibility="Collapsed"/>

                                <Button Grid.Column="3"
                                        Name="OpenMultiThreadWindowBtn"
                                        Content="🚀 多线程管理"
                                        Style="{StaticResource ModernButtonStyle}"
                                        Background="#805AD5"
                                        FontSize="12"
                                        Padding="8,4"
                                        Visibility="Collapsed"
                                        Click="OpenMultiThreadWindowBtn_Click"/>
                            </Grid>

                            <!-- AdsPower 配置 -->
                            <Grid Name="AdsPowerConfigGrid" Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0"
                                           Text="Adspower实例ID:"
                                           VerticalAlignment="Center"
                                           Margin="0,0,16,0"
                                           FontSize="14"
                                           FontWeight="Medium"
                                           Foreground="#2D3748"/>
                                <Border Grid.Column="1"
                                        Background="White"
                                        BorderBrush="#E2E8F0"
                                        BorderThickness="2"
                                        CornerRadius="8"
                                        Margin="0,0,8,0"
                                        Height="32">
                                    <TextBox Name="ProfileIdTextBox"
                                             Text=""
                                             VerticalContentAlignment="Center"
                                             Padding="8,0"
                                             FontSize="12"
                                             Background="Transparent"
                                             BorderThickness="0"/>
                                </Border>
                                <Button Grid.Column="2"
                                        Name="TestConnectionButton"
                                        Content="🔗 测试连接"
                                        Click="TestConnectionButton_Click"
                                        Margin="0,0,8,0"
                                        Height="32"
                                        Style="{StaticResource ModernButtonStyle}"
                                        Background="#ED8936"/>

                            </Grid>

                            <!-- 本地 Chrome 配置 -->
                            <Grid Name="LocalChromeConfigGrid" Visibility="Collapsed" Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0"
                                           Text="将使用系统安装的 Google Chrome 浏览器进行自动化操作"
                                           VerticalAlignment="Center"
                                           FontSize="13"
                                           Foreground="#4A5568"
                                           TextWrapping="Wrap"/>
                                <Button Grid.Column="1"
                                        Name="TestLocalChromeButton"
                                        Content="🚀 测试启动"
                                        Click="TestLocalChromeButton_Click"
                                        Height="32"
                                        Style="{StaticResource ModernButtonStyle}"
                                        Background="#e66465"/>
                            </Grid>

                            <!-- 分隔线 -->
                            <Separator Margin="0,16,0,12" Background="#E2E8F0" Height="2"/>

                            <!-- PhoneApi 配置 -->
                            <Grid Margin="0,0,0,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- 标题 -->
                                <Grid Grid.Row="0" Margin="0,0,0,10">
                                    <TextBlock Text="📱 手机号码获取配置"
                                               FontWeight="SemiBold"
                                               VerticalAlignment="Center"
                                               FontSize="14"
                                               Foreground="#2D3748"/>
                                </Grid>

                                <!-- 手机号码模式选择 -->
                                <Grid Grid.Row="1" Margin="0,0,0,12">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0"
                                               Text="获取模式:"
                                               VerticalAlignment="Center"
                                               Margin="0,0,20,0"
                                               FontSize="14"
                                               FontWeight="Medium"
                                               Foreground="#2D3748"/>
                                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                                        <RadioButton Name="PhoneDurianModeRadio"
                                                     Content="榴莲"
                                                     Margin="0,0,20,0"
                                                     FontSize="13"
                                                     Checked="PhoneModeRadio_Checked"/>
                                        <RadioButton Name="PhoneQianchuanModeRadio"
                                                     Content="千川"
                                                     Margin="0,0,20,0"
                                                     FontSize="13"
                                                     Checked="PhoneModeRadio_Checked"/>
                                        <RadioButton Name="PhoneUsaModeRadio"
                                                     Content="美国"
                                                     Margin="0,0,20,0"
                                                     FontSize="13"
                                                     Checked="PhoneModeRadio_Checked"/>
                                        <RadioButton Name="PhoneManualModeRadio"
                                                     Content="手动模式"
                                                     FontSize="13"
                                                     Checked="PhoneModeRadio_Checked"/>
                                    </StackPanel>

                                    <!-- 模式指示器 -->
                                    <Border Grid.Column="2"
                                            Background="#E2E8F0"
                                            CornerRadius="12"
                                            Padding="8,4"
                                            Margin="16,0,0,0">
                                        <StackPanel Orientation="Horizontal">
                                            <Ellipse Name="PhoneModeIndicator"
                                                     Width="8"
                                                     Height="8"
                                                     Fill="#48BB78"
                                                     Margin="0,0,6,0"/>
                                            <TextBlock Name="PhoneModeText"
                                                       Text="自动获取"
                                                       FontSize="11"
                                                       FontWeight="Medium"
                                                       Foreground="#2D3748"/>
                                        </StackPanel>
                                    </Border>
                                </Grid>

                                <!-- API配置 -->
                                <Grid Grid.Row="2" Name="PhoneApiConfigGrid" Visibility="Collapsed" Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0"
                                               Text="用户名:"
                                               VerticalAlignment="Center"
                                               Margin="0,0,12,0"
                                               FontSize="13"
                                               FontWeight="Medium"
                                               Foreground="#2D3748"/>
                                    <Border Grid.Column="1"
                                            Background="White"
                                            BorderBrush="#E2E8F0"
                                            BorderThickness="2"
                                            CornerRadius="6"
                                            Margin="0,0,16,0"
                                            Height="28">
                                        <TextBox Name="PhoneApiNameTextBox"
                                                 VerticalContentAlignment="Center"
                                                 Padding="8,0"
                                                 FontSize="11"
                                                 Background="Transparent"
                                                 BorderThickness="0"/>
                                    </Border>

                                    <TextBlock Grid.Column="2"
                                               Text="API密钥:"
                                               VerticalAlignment="Center"
                                               Margin="0,0,12,0"
                                               FontSize="13"
                                               FontWeight="Medium"
                                               Foreground="#2D3748"/>
                                    <Border Grid.Column="3"
                                            Background="White"
                                            BorderBrush="#E2E8F0"
                                            BorderThickness="2"
                                            CornerRadius="6"
                                            Height="28">
                                        <TextBox Name="PhoneApiKeyTextBox"
                                                 VerticalContentAlignment="Center"
                                                 Padding="8,0"
                                                 FontSize="11"
                                                 Background="Transparent"
                                                 BorderThickness="0"/>
                                    </Border>
                                </Grid>

                                <!-- 项目ID和国家选择 -->
                                <Grid Grid.Row="3" Name="PhoneApiConfigGrid2" Visibility="Collapsed" Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0"
                                               Text="项目ID:"
                                               VerticalAlignment="Center"
                                               Margin="0,0,12,0"
                                               FontSize="13"
                                               FontWeight="Medium"
                                               Foreground="#2D3748"/>
                                    <Border Grid.Column="1"
                                            Background="White"
                                            BorderBrush="#E2E8F0"
                                            BorderThickness="2"
                                            CornerRadius="6"
                                            Margin="0,0,16,0"
                                            Height="28">
                                        <TextBox Name="PhoneApiProjectIdTextBox"
                                                 Text="0209"
                                                 VerticalContentAlignment="Center"
                                                 Padding="8,0"
                                                 FontSize="11"
                                                 Background="Transparent"
                                                 BorderThickness="0"/>
                                    </Border>

                                    <TextBlock Grid.Column="2"
                                               Text="国家/地区:"
                                               VerticalAlignment="Center"
                                               Margin="0,0,12,0"
                                               FontSize="13"
                                               FontWeight="Medium"
                                               Foreground="#2D3748"/>
                                    <Grid Grid.Column="3">
                                        <!-- 搜索输入框 -->
                                        <TextBox Name="CountrySearchTextBox"
                                                 Height="35"
                                                 Padding="8,6"
                                                 BorderBrush="#CBD5E0"
                                                 BorderThickness="1"
                                                 Background="White"
                                                 FontSize="13"
                                                 Text="点击搜索国家..."
                                                 Foreground="#A0AEC0"
                                                 GotFocus="CountrySearchTextBox_GotFocus"
                                                 LostFocus="CountrySearchTextBox_LostFocus"
                                                 TextChanged="CountrySearchTextBox_TextChanged"
                                                 KeyDown="CountrySearchTextBox_KeyDown"/>

                                        <!-- 悬浮的搜索结果列表 -->
                                        <Popup Name="SearchResultsPopup"
                                               PlacementTarget="{Binding ElementName=CountrySearchTextBox}"
                                               Placement="Bottom"
                                               IsOpen="False"
                                               AllowsTransparency="True"
                                               StaysOpen="True"
                                               PopupAnimation="Slide">
                                            <Border Background="White"
                                                    BorderBrush="#CBD5E0"
                                                    BorderThickness="1"
                                                    CornerRadius="6"
                                                    MaxHeight="200"
                                                    MinWidth="300"
                                                    >
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#000000"
                                                                      Opacity="0.1"
                                                                      ShadowDepth="4"
                                                                      BlurRadius="12"/>
                                                </Border.Effect>
                                                <ListBox Name="SearchResultsListBox"
                                                         Background="Transparent"
                                                         BorderThickness="0"
                                                         SelectionChanged="SearchResultsListBox_SelectionChanged"
                                                         ScrollViewer.VerticalScrollBarVisibility="Auto"
                                                         ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                                         ScrollViewer.CanContentScroll="False">
                                                    <ListBox.ItemTemplate>
                                                        <DataTemplate>
                                                            <TextBlock Text="{Binding Name}"
                                                                       Padding="8,6"
                                                                       FontSize="13"
                                                                       TextWrapping="NoWrap"/>
                                                        </DataTemplate>
                                                    </ListBox.ItemTemplate>
                                                    <ListBox.ItemContainerStyle>
                                                        <Style TargetType="ListBoxItem">
                                                            <Setter Property="Padding" Value="0"/>
                                                            <Setter Property="Margin" Value="0"/>
                                                            <Setter Property="BorderThickness" Value="0"/>
                                                            <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                                            <Style.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="Background" Value="#F7FAFC"/>
                                                                </Trigger>
                                                                <Trigger Property="IsSelected" Value="True">
                                                                    <Setter Property="Background" Value="#EDF2F7"/>
                                                                </Trigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </ListBox.ItemContainerStyle>
                                                </ListBox>
                                            </Border>
                                        </Popup>
                                    </Grid>
                                </Grid>

                                <!-- 保存按钮 -->
                                <Grid Grid.Row="4" Name="PhoneApiConfigGrid3" Visibility="Collapsed">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <Button Grid.Column="1"
                                            Name="TestPhoneApiButton"
                                            Content="🧪 测试API"
                                            Click="TestPhoneApiButton_Click"
                                            Margin="0,0,8,0"
                                            Height="28"
                                            Style="{StaticResource ModernButtonStyle}"
                                            Background="#8B5A3C"/>
                                    <Button Grid.Column="2"
                                            Name="SavePhoneApiButton"
                                            Content="💾 保存配置"
                                            Click="SavePhoneApiButton_Click"
                                            Height="28"
                                            Style="{StaticResource ModernButtonStyle}"
                                            Background="#48BB78"/>
                                </Grid>

                                <!-- 分隔线 -->
                                <Separator Grid.Row="5" Margin="0,16,0,12" Background="#E2E8F0" Height="2"/>

                                <!-- 邮箱验证码配置 -->
                                <Grid Grid.Row="6" Margin="0,0,0,0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- 标题 -->
                                    <Grid Grid.Row="0" Margin="0,0,0,10">
                                        <TextBlock Text="📧 邮箱验证码配置"
                                                   FontWeight="SemiBold"
                                                   VerticalAlignment="Center"
                                                   FontSize="14"
                                                   Foreground="#2D3748"/>
                                    </Grid>

                                    <!-- 邮箱验证码模式选择 -->
                                    <Grid Grid.Row="1" Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0"
                                                   Text="获取模式:"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,20,0"
                                                   FontSize="14"
                                                   FontWeight="Medium"
                                                   Foreground="#2D3748"/>
                                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                                            <RadioButton Name="EmailGoogleRadio"
                                                         Content="谷歌"
                                                         Margin="0,0,32,0"
                                                         FontSize="13"
                                                         Checked="EmailModeRadio_Checked"/>
                                            <RadioButton Name="EmailMicrosoftRadio"
                                                         Content="微软"
                                                         Margin="0,0,32,0"
                                                         FontSize="13"
                                                         Checked="EmailModeRadio_Checked"/>
                                            <RadioButton Name="EmailManualModeRadio"
                                                         Content="手动输入"
                                                         FontSize="13"
                                                         Checked="EmailModeRadio_Checked"/>
                                        </StackPanel>
                                    </Grid>

                                    <!-- 说明文本 -->
                                    <Border Grid.Row="2" Background="#EBF8FF"
                                            BorderBrush="#4299E1"
                                            BorderThickness="1"
                                            CornerRadius="6"
                                            Padding="12"
                                            Margin="0,0,0,8">
                                        <StackPanel>
                                            <TextBlock Text="📋 使用说明："
                                                       FontSize="12"
                                                       FontWeight="SemiBold"
                                                       Foreground="#2B6CB0"
                                                       Margin="0,0,0,6"/>
                                            <TextBlock Name="EmailModeDescriptionText"
                                                       Text="• 谷歌：使用谷歌邮箱API自动获取验证码&#x0a;• 微软：使用微软邮箱验证码获取工具（需要先启动工具并预登录邮箱账号）&#x0a;• 手动模式：需要您手动输入收到的邮箱验证码"
                                                       FontSize="11"
                                                       Foreground="#2B6CB0"
                                                       TextWrapping="Wrap"
                                                       LineHeight="16"/>
                                        </StackPanel>
                                    </Border>
                                </Grid>

                                <!-- 分隔线 -->
                                <Separator Grid.Row="7" Margin="0,16,0,12" Background="#E2E8F0" Height="2"/>

                                <!-- 图形验证码配置 -->
                                <Grid Grid.Row="8" Margin="0,0,0,0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- 标题 -->
                                    <Grid Grid.Row="0" Margin="0,0,0,10">
                                        <TextBlock Text="🔍 图形验证码配置"
                                                   FontWeight="SemiBold"
                                                   VerticalAlignment="Center"
                                                   FontSize="14"
                                                   Foreground="#2D3748"/>
                                    </Grid>

                                    <!-- 图形验证码模式选择 -->
                                    <Grid Grid.Row="1" Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0" Text="模式选择："
                                                   VerticalAlignment="Center"
                                                   FontSize="12"
                                                   FontWeight="Medium"
                                                   Foreground="#4A5568"
                                                   Margin="0,0,12,0"/>

                                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                                            <RadioButton Name="CaptchaYesModeRadio"
                                                         Content="Yes打码"
                                                         FontSize="12"
                                                         FontWeight="Medium"
                                                         Foreground="#2D3748"
                                                         Margin="0,0,20,0"
                                                         Checked="CaptchaYesModeRadio_Checked"/>
                                            <RadioButton Name="CaptchaCloudModeRadio"
                                                         Content="云打码"
                                                         FontSize="12"
                                                         FontWeight="Medium"
                                                         Foreground="#2D3748"
                                                         Margin="0,0,20,0"
                                                         Checked="CaptchaCloudModeRadio_Checked"/>
                                            <RadioButton Name="CaptchaManualModeRadio"
                                                         Content="手动模式"
                                                         FontSize="12"
                                                         FontWeight="Medium"
                                                         Foreground="#2D3748"
                                                         Checked="CaptchaManualModeRadio_Checked"/>
                                        </StackPanel>
                                    </Grid>

                                    <!-- 说明信息 -->
                                    <Grid Grid.Row="2">
                                        <Border Background="#EBF8FF"
                                                BorderBrush="#BEE3F8"
                                                BorderThickness="1"
                                                CornerRadius="6"
                                                Padding="12">
                                            <StackPanel>
                                                <TextBlock Text="📋 模式说明"
                                                           FontSize="12"
                                                           FontWeight="SemiBold"
                                                           Foreground="#2B6CB0"
                                                           Margin="0,0,0,6"/>
                                                <TextBlock Name="CaptchaModeDescriptionText"
                                                           Text="• Yes打码：使用Yes打码平台自动识别图形验证码并填入&#x0a;• 云打码：使用云打码平台自动识别图形验证码并填入&#x0a;• 手动模式：需要您手动完成图形验证码"
                                                           FontSize="11"
                                                           Foreground="#2B6CB0"
                                                           TextWrapping="Wrap"
                                                           LineHeight="16"/>
                                            </StackPanel>
                                        </Border>
                                    </Grid>
                                </Grid>
                            </Grid>
                        </StackPanel>
                    </GroupBox>

                    <!-- 控制按钮区域 -->
                    <GroupBox Grid.Row="2" Header="🎮 操作控制"
                              Padding="12" Margin="0,0,0,10"
                              FontSize="14" FontWeight="SemiBold"
                              Background="#FAFBFC"
                              BorderBrush="#E2E8F0"
                              BorderThickness="1">
                        <GroupBox.Effect>
                            <DropShadowEffect Color="#000000"
                                              Opacity="0.05"
                                              ShadowDepth="2"
                                              BlurRadius="8"/>
                        </GroupBox.Effect>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Button Grid.Column="0" Name="StartButton"
                                    Content="▶ 开始注册"
                                    Margin="0,0,4,0"
                                    Height="36"
                                    FontSize="12"
                                    FontWeight="Bold"
                                    Click="StartButton_Click"
                                    IsEnabled="False"
                                    Style="{StaticResource ModernButtonStyle}"
                                    Background="#48BB78"/>

                            <Button Grid.Column="1" Name="ContinueButton"
                                    Content="⏩ 继续注册"
                                    Margin="2,0"
                                    Height="36"
                                    FontSize="12"
                                    FontWeight="Bold"
                                    Click="ContinueButton_Click"
                                    IsEnabled="False"
                                    Style="{StaticResource ModernButtonStyle}"
                                    Background="#ED8936"/>

                            <Button Grid.Column="2" Name="PauseButton"
                                    Content="⏸ 暂停注册"
                                    Margin="2,0"
                                    Height="36"
                                    FontSize="12"
                                    FontWeight="Bold"
                                    Click="PauseButton_Click"
                                    IsEnabled="False"
                                    Style="{StaticResource ModernButtonStyle}"
                                    Background="#4299E1"/>

                            <Button Grid.Column="3" Name="StopButton"
                                    Content="⏹ 终止注册"
                                    Margin="4,0,0,0"
                                    Height="36"
                                    FontSize="12"
                                    FontWeight="Bold"
                                    Click="StopButton_Click"
                                    IsEnabled="False"
                                    Style="{StaticResource ModernButtonStyle}"
                                    Background="#F56565"/>
                        </Grid>
                    </GroupBox>

                    <!-- 状态显示区域 -->
                    <GroupBox Grid.Row="3" Header="💻 系统状态"
                              Padding="12"
                              FontSize="14" FontWeight="SemiBold"
                              Background="#FAFBFC"
                              BorderBrush="#E2E8F0"
                              BorderThickness="1">
                        <GroupBox.Effect>
                            <DropShadowEffect Color="#000000"
                                              Opacity="0.05"
                                              ShadowDepth="2"
                                              BlurRadius="8"/>
                        </GroupBox.Effect>
                        <Border Background="White"
                                Padding="12"
                                CornerRadius="8"
                                BorderBrush="#E2E8F0"
                                BorderThickness="1">
                            <Border.Effect>
                                <DropShadowEffect Color="#000000"
                                                  Opacity="0.05"
                                                  ShadowDepth="2"
                                                  BlurRadius="8"/>
                            </Border.Effect>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0"
                                           Text="📊"
                                           FontSize="16"
                                           VerticalAlignment="Center"
                                           Margin="0,0,12,0"/>
                                <TextBlock Grid.Column="1"
                                           Name="StatusTextBlock"
                                           Text="程序已就绪 - 双模式浏览器支持已启用"
                                           TextWrapping="Wrap"
                                           VerticalAlignment="Center"
                                           FontSize="12"
                                           Foreground="#4A5568"/>

                                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                                    <!--<Button Name="RunTestsBtn"
                                            Content="🧪 运行测试"
                                            Style="{StaticResource ModernButtonStyle}"
                                            Background="#9F7AEA"
                                            FontSize="11"
                                            Padding="8,4"
                                            Margin="0,0,12,0"
                                            Click="RunTestsBtn_Click"
                                            ToolTip="运行优化功能测试和多线程功能集成测试"/>-->

                                    <TextBlock Text="当前模式:"
                                               FontSize="13"
                                               Margin="0,0,12,0"
                                               VerticalAlignment="Center"
                                               Foreground="#718096"
                                               FontWeight="Medium"/>
                                    <Border Name="BrowserModeIndicator"
                                            Background="#4299E1"
                                            Padding="8,4"
                                            CornerRadius="6">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#4299E1"
                                                              Opacity="0.2"
                                                              ShadowDepth="2"
                                                              BlurRadius="6"/>
                                        </Border.Effect>
                                        <TextBlock Name="BrowserModeText"
                                                   Text="AdsPower"
                                                   FontSize="10"
                                                   Foreground="White"
                                                   FontWeight="SemiBold"/>
                                    </Border>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </GroupBox>

                    <!-- 数据显示区域 -->
                    <GroupBox Grid.Row="4" Header="📊 数据状态监控"
                              Padding="12" Margin="0,0,0,12"
                              FontSize="14" FontWeight="SemiBold"
                              Background="#FAFBFC"
                              BorderBrush="#E2E8F0"
                              BorderThickness="1">
                        <GroupBox.Effect>
                            <DropShadowEffect Color="#000000"
                                              Opacity="0.05"
                                              ShadowDepth="2"
                                              BlurRadius="8"/>
                        </GroupBox.Effect>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*" MinHeight="120"/>
                            </Grid.RowDefinitions>

                            <!-- 数据统计面板 -->
                            <Border Grid.Row="0"
                                    Background="White"
                                    Padding="12"
                                    Margin="0,0,0,10"
                                    CornerRadius="8"
                                    BorderBrush="#E2E8F0"
                                    BorderThickness="1">
                                <Border.Effect>
                                    <DropShadowEffect Color="#000000"
                                                      Opacity="0.05"
                                                      ShadowDepth="2"
                                                      BlurRadius="8"/>
                                </Border.Effect>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0"
                                            Background="#F7FAFC"
                                            Padding="8"
                                            Margin="0,0,2,0"
                                            CornerRadius="8"
                                            BorderBrush="#A0AEC0"
                                            BorderThickness="2">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#A0AEC0"
                                                              Opacity="0.1"
                                                              ShadowDepth="2"
                                                              BlurRadius="8"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="⚪" FontSize="16" HorizontalAlignment="Center" Margin="0,0,0,2"/>
                                            <TextBlock Text="未处理" FontSize="10" HorizontalAlignment="Center" Foreground="#4A5568" FontWeight="SemiBold"/>
                                            <TextBlock Name="UnprocessedCountTextBlock" Text="0" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#4A5568" Margin="0,2,0,0"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Grid.Column="1"
                                            Background="#EBF8FF"
                                            Padding="8"
                                            Margin="2,0"
                                            CornerRadius="8"
                                            BorderBrush="#4299E1"
                                            BorderThickness="2">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#4299E1"
                                                              Opacity="0.1"
                                                              ShadowDepth="2"
                                                              BlurRadius="8"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="⏳" FontSize="16" HorizontalAlignment="Center" Margin="0,0,0,2"/>
                                            <TextBlock Text="待处理" FontSize="10" HorizontalAlignment="Center" Foreground="#2B6CB0" FontWeight="SemiBold"/>
                                            <TextBlock Name="PendingCountTextBlock" Text="0" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#2B6CB0" Margin="0,2,0,0"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Grid.Column="2"
                                            Background="#FFFAF0"
                                            Padding="8"
                                            Margin="2,0"
                                            CornerRadius="8"
                                            BorderBrush="#ED8936"
                                            BorderThickness="2">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#ED8936"
                                                              Opacity="0.1"
                                                              ShadowDepth="2"
                                                              BlurRadius="8"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="⚡" FontSize="16" HorizontalAlignment="Center" Margin="0,0,0,2"/>
                                            <TextBlock Text="处理中" FontSize="10" HorizontalAlignment="Center" Foreground="#C05621" FontWeight="SemiBold"/>
                                            <TextBlock Name="ProcessingCountTextBlock" Text="0" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#C05621" Margin="0,2,0,0"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Grid.Column="3"
                                            Background="#F0FFF4"
                                            Padding="8"
                                            Margin="2,0"
                                            CornerRadius="8"
                                            BorderBrush="#48BB78"
                                            BorderThickness="2">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#48BB78"
                                                              Opacity="0.1"
                                                              ShadowDepth="2"
                                                              BlurRadius="8"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="✅" FontSize="16" HorizontalAlignment="Center" Margin="0,0,0,2"/>
                                            <TextBlock Text="已完成" FontSize="10" HorizontalAlignment="Center" Foreground="#2F855A" FontWeight="SemiBold"/>
                                            <TextBlock Name="CompletedCountTextBlock" Text="0" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#2F855A" Margin="0,2,0,0"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Grid.Column="4"
                                            Background="#FFF5F5"
                                            Padding="8"
                                            Margin="2,0,0,0"
                                            CornerRadius="8"
                                            BorderBrush="#F56565"
                                            BorderThickness="2">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#F56565"
                                                              Opacity="0.1"
                                                              ShadowDepth="2"
                                                              BlurRadius="8"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="❌" FontSize="16" HorizontalAlignment="Center" Margin="0,0,0,2"/>
                                            <TextBlock Text="失败" FontSize="10" HorizontalAlignment="Center" Foreground="#C53030" FontWeight="SemiBold"/>
                                            <TextBlock Name="FailedCountTextBlock" Text="0" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#C53030" Margin="0,2,0,0"/>
                                        </StackPanel>
                                    </Border>
                                </Grid>
                            </Border>

                            <!-- 数据表格 -->
                            <Border Grid.Row="1"
                                    Background="White"
                                    CornerRadius="12"
                                    BorderBrush="#E2E8F0"
                                    BorderThickness="1">
                                <Border.Effect>
                                    <DropShadowEffect Color="#000000"
                                                      Opacity="0.05"
                                                      ShadowDepth="2"
                                                      BlurRadius="8"/>
                                </Border.Effect>
                                <DataGrid Name="DataGrid"
                                          AutoGenerateColumns="False"
                                          CanUserAddRows="False"
                                          CanUserDeleteRows="False"
                                          IsReadOnly="True"
                                          FontSize="10"
                                          RowHeight="24"
                                          HeadersVisibility="Column"
                                          GridLinesVisibility="Horizontal"
                                          AlternatingRowBackground="#FAFBFC"
                                          Background="Transparent"
                                          BorderThickness="0"
                                          CanUserResizeColumns="True"
                                          CanUserSortColumns="True"
                                          Margin="1"
                                          MaxHeight="144"
                                          ScrollViewer.VerticalScrollBarVisibility="Auto"
                                          ScrollViewer.HorizontalScrollBarVisibility="Visible"
                                          ScrollViewer.CanContentScroll="False">
                                    <DataGrid.ColumnHeaderStyle>
                                        <Style TargetType="DataGridColumnHeader">
                                            <Setter Property="Background" Value="#F7FAFC"/>
                                            <Setter Property="Foreground" Value="#2D3748"/>
                                            <Setter Property="FontWeight" Value="SemiBold"/>
                                            <Setter Property="FontSize" Value="10"/>
                                            <Setter Property="Padding" Value="8,4"/>
                                            <Setter Property="BorderBrush" Value="#E2E8F0"/>
                                            <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                        </Style>
                                    </DataGrid.ColumnHeaderStyle>
                                    <DataGrid.RowStyle>
                                        <Style TargetType="DataGridRow">
                                            <Setter Property="Background" Value="Transparent"/>
                                            <Setter Property="BorderThickness" Value="0"/>
                                            <Style.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#EDF2F7"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </DataGrid.RowStyle>
                                    <DataGrid.CellStyle>
                                        <Style TargetType="DataGridCell">
                                            <Setter Property="BorderThickness" Value="0"/>
                                            <Setter Property="Padding" Value="6,3"/>
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="DataGridCell">
                                                        <Border Background="{TemplateBinding Background}"
                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                                Padding="{TemplateBinding Padding}">
                                                            <ContentPresenter VerticalAlignment="Center"/>
                                                        </Border>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </DataGrid.CellStyle>
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="状态" Binding="{Binding StatusText}" Width="60">
                                            <DataGridTextColumn.ElementStyle>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="Unprocessed">
                                                            <Setter Property="Foreground" Value="#4A5568"/>
                                                            <Setter Property="FontWeight" Value="Medium"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="Pending">
                                                            <Setter Property="Foreground" Value="#3182CE"/>
                                                            <Setter Property="FontWeight" Value="Medium"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="Processing">
                                                            <Setter Property="Foreground" Value="#38A169"/>
                                                            <Setter Property="FontWeight" Value="Bold"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="Completed">
                                                            <Setter Property="Foreground" Value="#E53E3E"/>
                                                            <Setter Property="FontWeight" Value="Medium"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="Failed">
                                                            <Setter Property="Foreground" Value="#E53E3E"/>
                                                            <Setter Property="FontWeight" Value="Medium"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="Paused">
                                                            <Setter Property="Foreground" Value="#38A169"/>
                                                            <Setter Property="FontWeight" Value="Bold"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </DataGridTextColumn.ElementStyle>
                                        </DataGridTextColumn>
                                        <DataGridTextColumn Header="邮箱地址" Binding="{Binding Email}" Width="180"/>
                                        <DataGridTextColumn Header="密码" Binding="{Binding Password}" Width="80"/>
                                        <DataGridTextColumn Header="全名" Binding="{Binding FullName}" Width="100"/>
                                        <DataGridTextColumn Header="组织名称" Binding="{Binding OrganizationName}" Width="120"/>
                                        <DataGridTextColumn Header="地址行1" Binding="{Binding Address}" Width="150"/>
                                        <DataGridTextColumn Header="城市" Binding="{Binding City}" Width="80"/>
                                        <DataGridTextColumn Header="州/省" Binding="{Binding State}" Width="60"/>
                                        <DataGridTextColumn Header="邮编" Binding="{Binding PostalCode}" Width="60"/>
                                        <DataGridTextColumn Header="信用卡号" Binding="{Binding CardNumber}" Width="120"/>
                                        <DataGridTextColumn Header="到期月份" Binding="{Binding ExpiryMonth}" Width="70"/>
                                        <DataGridTextColumn Header="到期年份" Binding="{Binding ExpiryYear}" Width="70"/>
                                        <DataGridTextColumn Header="安全码" Binding="{Binding SecurityCode}" Width="60"/>
                                        <DataGridTextColumn Header="持卡人姓名" Binding="{Binding CardholderName}" Width="100"/>
                                        <DataGridTextColumn Header="邮箱密码" Binding="{Binding EmailPassword}" Width="100"/>
                                        <DataGridTextColumn Header="国家代码" Binding="{Binding CountryCode}" Width="80"/>
                                        </DataGrid.Columns>
                                </DataGrid>
                            </Border>
                        </Grid>
                    </GroupBox>

                    <!-- 操作进度区域 - 暂时注释 -->
                    <!--
                    <GroupBox Grid.Row="4" Header="📈 操作进度"
                              Padding="12"
                              FontSize="14" FontWeight="SemiBold"
                              Background="#FAFBFC"
                              BorderBrush="#E2E8F0"
                              BorderThickness="1"
                              Margin="0,8,0,8">
                        <GroupBox.Effect>
                            <DropShadowEffect Color="#000000"
                                              Opacity="0.05"
                                              ShadowDepth="2"
                                              BlurRadius="8"/>
                        </GroupBox.Effect>
                        <Border Background="White"
                                CornerRadius="8"
                                Padding="16">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,16,0">
                                    <TextBlock Text="总体进度"
                                               FontSize="12"
                                               FontWeight="Medium"
                                               Foreground="#4A5568"
                                               Margin="0,0,0,4"/>
                                    <ProgressBar Name="OverallProgressBar"
                                                 Height="8"
                                                 Background="#E2E8F0"
                                                 Foreground="#48BB78"
                                                 BorderThickness="0"
                                                 Value="0"
                                                 Maximum="100"/>
                                    <TextBlock Name="OverallProgressText"
                                               Text="0%"
                                               FontSize="11"
                                               Foreground="#718096"
                                               HorizontalAlignment="Center"
                                               Margin="0,2,0,0"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="0,0,16,0">
                                    <TextBlock Text="当前步骤"
                                               FontSize="12"
                                               FontWeight="Medium"
                                               Foreground="#4A5568"
                                               Margin="0,0,0,4"/>
                                    <ProgressBar Name="CurrentStepProgressBar"
                                                 Height="8"
                                                 Background="#E2E8F0"
                                                 Foreground="#4299E1"
                                                 BorderThickness="0"
                                                 Value="0"
                                                 Maximum="100"/>
                                    <TextBlock Name="CurrentStepProgressText"
                                               Text="待开始"
                                               FontSize="11"
                                               Foreground="#718096"
                                               HorizontalAlignment="Center"
                                               Margin="0,2,0,0"/>
                                </StackPanel>

                                <StackPanel Grid.Column="2" Margin="0,0,16,0">
                                    <TextBlock Text="处理速度"
                                               FontSize="12"
                                               FontWeight="Medium"
                                               Foreground="#4A5568"
                                               Margin="0,0,0,4"/>
                                    <Border Background="#F7FAFC"
                                            CornerRadius="6"
                                            Padding="8,4"
                                            Height="24">
                                        <TextBlock Name="ProcessingSpeedText"
                                                   Text="0 条/分钟"
                                                   FontSize="11"
                                                   Foreground="#2D3748"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"/>
                                    </Border>
                                </StackPanel>

                                <StackPanel Grid.Column="3">
                                    <TextBlock Text="预计剩余"
                                               FontSize="12"
                                               FontWeight="Medium"
                                               Foreground="#4A5568"
                                               Margin="0,0,0,4"/>
                                    <Border Background="#F7FAFC"
                                            CornerRadius="6"
                                            Padding="8,4"
                                            Height="24">
                                        <TextBlock Name="EstimatedTimeText"
                                                   Text="00:00"
                                                   FontSize="11"
                                                   Foreground="#2D3748"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"/>
                                    </Border>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </GroupBox>
                    -->
                </Grid>
            </ScrollViewer>
            </Border>
        </Grid>
</Window>
