using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Collections.Generic;
using System.Linq;

namespace AWSAutoRegister.Services
{
    public class PhoneApiService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _name;
        private readonly string _apiKey;
        private readonly string _projectId;
        private readonly string _countryCode;

        public PhoneApiService(string baseUrl, string name, string apiKey, string projectId, string countryCode = "th")
        {
            _httpClient = new HttpClient();
            // 设置超时时间为10秒，避免长时间等待
            _httpClient.Timeout = TimeSpan.FromSeconds(10);
            _baseUrl = baseUrl;
            _name = name;
            _apiKey = apiKey;
            _projectId = projectId;
            _countryCode = countryCode;
        }

        public class ApiResponse<T>
        {
            [JsonPropertyName("code")]
            public int Code { get; set; }

            [JsonPropertyName("msg")]
            public string Message { get; set; } = string.Empty;

            [JsonPropertyName("data")]
            public T? Data { get; set; }
        }

        public class PhoneNumberInfo
        {
            public string FullNumber { get; set; } = string.Empty;
            public string CountryCode { get; set; } = string.Empty;
            public string LocalNumber { get; set; } = string.Empty;
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        public async Task<ApiResponse<object>> GetUserInfoAsync()
        {
            try
            {
                var url = $"{_baseUrl}/getUserInfo?name={_name}&ApiKey={_apiKey}";
                var response = await _httpClient.GetStringAsync(url);
                return JsonSerializer.Deserialize<ApiResponse<object>>(response) ?? new ApiResponse<object>();
            }
            catch (Exception ex)
            {
                return new ApiResponse<object> { Code = -1, Message = $"请求失败: {ex.Message}" };
            }
        }

        /// <summary>
        /// 获取手机号码（单条）
        /// </summary>
        public async Task<(bool Success, PhoneNumberInfo? PhoneInfo, string Message)> GetPhoneNumberAsync()
        {
            return await ApiRetryService.ExecuteWithRetryTupleAsync(
                async () => await GetPhoneNumberInternalAsync(),
                "获取手机号码",
                "榴莲API"
            );
        }

        /// <summary>
        /// 内部获取手机号码方法（不包含重试逻辑）
        /// </summary>
        private async Task<(bool Success, PhoneNumberInfo? PhoneInfo, string Message)> GetPhoneNumberInternalAsync()
        {
            var url = $"{_baseUrl}/getMobileCode?name={_name}&ApiKey={_apiKey}&cuy={_countryCode}&pid={_projectId}&num=1&noblack=0&serial=2&secret_key=null&vip=null";

            System.Diagnostics.Debug.WriteLine($"[榴莲API] 请求URL: {url}");
            Console.WriteLine($"[榴莲API] 请求URL: {url}");

            var response = await _httpClient.GetStringAsync(url);

            System.Diagnostics.Debug.WriteLine($"[榴莲API] 响应内容: {response}");
            Console.WriteLine($"[榴莲API] 响应内容: {response}");

            var result = JsonSerializer.Deserialize<ApiResponse<string>>(response);

            if (result?.Code == 200 && !string.IsNullOrEmpty(result.Data))
            {
                // 解析返回的数据格式："+59173841704,+591"
                var parts = result.Data.Split(',');
                if (parts.Length >= 2)
                {
                    var fullNumber = parts[0].Trim();
                    var countryCodePart = parts[1].Trim();

                    // 提取本地号码（去掉国家代码）
                    var localNumber = fullNumber;
                    if (fullNumber.StartsWith(countryCodePart))
                    {
                        localNumber = fullNumber.Substring(countryCodePart.Length);
                    }

                    var phoneInfo = new PhoneNumberInfo
                    {
                        FullNumber = fullNumber,
                        CountryCode = countryCodePart,
                        LocalNumber = localNumber
                    };

                    return (true, phoneInfo, "获取手机号码成功");
                }
                else
                {
                    return (false, null, "手机号码格式解析失败");
                }
            }
            else
            {
                var errorMsg = GetErrorMessage(result?.Code ?? -1);
                return (false, null, $"获取手机号码失败: {errorMsg}");
            }
        }

        /// <summary>
        /// 获取验证码
        /// </summary>
        public async Task<(bool Success, string? VerificationCode, string Message)> GetVerificationCodeAsync(string phoneNumber)
        {
            return await ApiRetryService.ExecuteWithRetryTupleAsync(
                async () => await GetVerificationCodeInternalAsync(phoneNumber),
                "获取验证码",
                "榴莲API"
            );
        }

        /// <summary>
        /// 内部获取验证码方法（不包含重试逻辑）
        /// </summary>
        private async Task<(bool Success, string? VerificationCode, string Message)> GetVerificationCodeInternalAsync(string phoneNumber)
        {
            var url = $"{_baseUrl}/getMsg?name={_name}&ApiKey={_apiKey}&pn={phoneNumber}&pid={_projectId}&serial=2";

            Console.WriteLine($"[榴莲API] 获取验证码请求: {phoneNumber}");

            var response = await _httpClient.GetStringAsync(url);
            var result = JsonSerializer.Deserialize<ApiResponse<string>>(response);

            if (result?.Code == 200 && !string.IsNullOrEmpty(result.Data))
            {
                return (true, result.Data, "获取验证码成功");
            }
            else if (result?.Code == 908)
            {
                return (false, null, "暂未查询到验证码，请稍后再试");
            }
            else
            {
                var errorMsg = GetErrorMessage(result?.Code ?? -1);
                return (false, null, $"获取验证码失败: {errorMsg}");
            }
        }



        /// <summary>
        /// 将手机号码加入黑名单
        /// </summary>
        public async Task<(bool Success, string Message)> AddToBlacklistAsync(string phoneNumber)
        {
            try
            {
                var url = $"{_baseUrl}/addBlack?name={_name}&ApiKey={_apiKey}&pn={phoneNumber}&pid={_projectId}";
                var response = await _httpClient.GetStringAsync(url);
                var result = JsonSerializer.Deserialize<ApiResponse<object>>(response);

                if (result?.Code == 200)
                {
                    return (true, "加入黑名单成功");
                }
                else
                {
                    var errorMsg = GetErrorMessage(result?.Code ?? -1);
                    return (false, $"加入黑名单失败: {errorMsg}");
                }
            }
            catch (Exception ex)
            {
                return (false, $"加入黑名单异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 查询国家手机号数量
        /// </summary>
        public async Task<(bool Success, object? Data, string Message)> GetCountryPhoneCountAsync()
        {
            try
            {
                var url = $"{_baseUrl}/getCountryPhoneNum?name={_name}&ApiKey={_apiKey}&pid={_projectId}&vip=null";
                var response = await _httpClient.GetStringAsync(url);
                var result = JsonSerializer.Deserialize<ApiResponse<object>>(response);

                if (result?.Code == 200)
                {
                    return (true, result.Data, "查询成功");
                }
                else
                {
                    var errorMsg = GetErrorMessage(result?.Code ?? -1);
                    return (false, null, $"查询失败: {errorMsg}");
                }
            }
            catch (Exception ex)
            {
                return (false, null, $"查询异常: {ex.Message}");
            }
        }

        private string GetErrorMessage(int code)
        {
            return code switch
            {
                200 => "成功",
                800 => "账号被封禁",
                802 => "用户名或ApiKey错误",
                803 => "用户名和ApiKey不能为空",
                902 => "传递的参数不正确",
                903 => "无效的国家代码",
                904 => "无效的项目ID",
                905 => "无效的手机号码",
                906 => "手机号列表为空",
                907 => "vip_key错误",
                908 => "暂未查询到验证码，请稍后再试",
                400 => "失败，系统异常",
                401 => "失败，无效操作",
                403 => "积分不足",
                405 => "验证码获取失败，请查询数据列表，或联系管理员",
                406 => "24小时内获得的新数量已达到最大数量",
                407 => "访问所有SMS、API请求刷新并重试",
                409 => "请求频率过高，请检查自己的代码或者联系管理员",
                912 => "请勿重复操作",
                400101 => "此项目需要密钥，请与管理员联系",
                400102 => "指定的参数未打开，请与管理员联系",
                400103 => "secret_key 错误",
                400906 => "无效系列或系列参数错误",
                200408 => "获取号码已达到上限，请联系管理员增加更多号码",
                _ => $"未知错误代码: {code}"
            };
        }

        /// <summary>
        /// 批量获取多个手机号码（多线程专用）
        /// </summary>
        public async Task<(bool Success, List<PhoneNumberInfo>? PhoneNumbers, string Message)> GetMultiplePhoneNumbersAsync(int count)
        {
            try
            {
                // 使用批量获取API：num=count, serial=2（多条数据）
                var url = $"{_baseUrl}/getMobile?name={_name}&ApiKey={_apiKey}&cuy={_countryCode}&pid={_projectId}&num={count}&noblack=0&serial=2&secret_key=null&vip=null";

                LogService.Instance.LogInfo($"[手机API] 批量获取{count}个手机号码，URL: {url}");

                var response = await _httpClient.GetStringAsync(url);

                // 添加调试信息，查看实际的API响应
                LogService.Instance.LogInfo($"[手机API] 批量获取响应内容: {response}");

                // 先尝试解析为通用的JsonElement，然后根据实际格式处理
                var genericResult = JsonSerializer.Deserialize<ApiResponse<JsonElement>>(response);

                if (genericResult?.Code == 200 && genericResult.Data.ValueKind != JsonValueKind.Null)
                {
                    var phoneNumbers = new List<PhoneNumberInfo>();

                    // 根据data字段的类型进行不同的处理
                    if (genericResult.Data.ValueKind == JsonValueKind.Array)
                    {
                        // 如果是数组，尝试解析每个元素
                        LogService.Instance.LogInfo($"[手机API] 检测到数组格式，元素数量: {genericResult.Data.GetArrayLength()}");

                        foreach (var element in genericResult.Data.EnumerateArray())
                        {
                            string phoneData = element.ValueKind == JsonValueKind.String
                                ? element.GetString() ?? ""
                                : element.ToString();

                            if (!string.IsNullOrEmpty(phoneData))
                            {
                                var phoneInfo = ParsePhoneData(phoneData);
                                if (phoneInfo != null)
                                {
                                    phoneNumbers.Add(phoneInfo);
                                }
                            }
                        }
                    }
                    else if (genericResult.Data.ValueKind == JsonValueKind.String)
                    {
                        // 如果是字符串，可能是逗号分隔的多个手机号
                        var dataString = genericResult.Data.GetString() ?? "";
                        LogService.Instance.LogInfo($"[手机API] 检测到字符串格式: {dataString}");

                        var phoneDataArray = dataString.Split(',', StringSplitOptions.RemoveEmptyEntries);
                        foreach (var phoneData in phoneDataArray)
                        {
                            var phoneInfo = ParsePhoneData(phoneData.Trim());
                            if (phoneInfo != null)
                            {
                                phoneNumbers.Add(phoneInfo);
                            }
                        }
                    }
                    else
                    {
                        LogService.Instance.LogError($"[手机API] 未知的data格式: {genericResult.Data.ValueKind}");
                        return (false, null, $"API返回了未知的数据格式: {genericResult.Data.ValueKind}");
                    }

                if (phoneNumbers.Count >= count)
                    {
                        LogService.Instance.LogInfo($"[手机API] 批量获取成功，获得{phoneNumbers.Count}个手机号码");
                        return (true, phoneNumbers.Take(count).ToList(), $"批量获取{phoneNumbers.Count}个手机号码成功");
                    }
                    else
                    {
                        LogService.Instance.LogError($"[手机API] 批量获取失败，期望{count}个，实际获得{phoneNumbers.Count}个");
                        return (false, null, $"批量获取失败，期望{count}个手机号码，实际获得{phoneNumbers.Count}个");
                    }
                }
                else
                {
                    var errorMsg = GetErrorMessage(genericResult?.Code ?? -1);
                    return (false, null, $"批量获取手机号码失败: {errorMsg}");
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogError($"[手机API] 批量获取异常: {ex.Message}");
                return (false, null, $"批量获取手机号码异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放手机号码（多线程专用，替代黑名单）
        /// </summary>
        public async Task<(bool Success, string Message)> ReleasePhoneNumberAsync(string phoneNumber)
        {
            try
            {
                var url = $"{_baseUrl}/passMobile?name={_name}&ApiKey={_apiKey}&pn={phoneNumber}&pid={_projectId}&serial=2";

                LogService.Instance.LogInfo($"[手机API] 释放手机号码: {phoneNumber}");

                var response = await _httpClient.GetStringAsync(url);
                var result = JsonSerializer.Deserialize<ApiResponse<string>>(response);

                if (result?.Code == 200)
                {
                    LogService.Instance.LogInfo($"[手机API] 手机号码释放成功: {phoneNumber}");
                    return (true, "手机号码释放成功");
                }
                else
                {
                    var errorMsg = GetErrorMessage(result?.Code ?? -1);
                    LogService.Instance.LogError($"[手机API] 手机号码释放失败: {phoneNumber}, 错误: {errorMsg}");
                    return (false, $"手机号码释放失败: {errorMsg}");
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogError($"[手机API] 手机号码释放异常: {phoneNumber}, 异常: {ex.Message}");
                return (false, $"手机号码释放异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析手机号码数据
        /// </summary>
        private PhoneNumberInfo? ParsePhoneData(string phoneData)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(phoneData))
                    return null;

                // 解析手机号码数据，格式可能是 "手机号,国家代码" 或只是 "手机号"
                var parts = phoneData.Split(',');
                if (parts.Length >= 2)
                {
                    var fullNumber = parts[0].Trim();
                    var countryCodePart = parts[1].Trim();
                    var localNumber = fullNumber.StartsWith(countryCodePart)
                        ? fullNumber.Substring(countryCodePart.Length)
                        : fullNumber;

                    return new PhoneNumberInfo
                    {
                        FullNumber = fullNumber,
                        CountryCode = countryCodePart,
                        LocalNumber = localNumber
                    };
                }
                else if (parts.Length == 1)
                {
                    // 只有手机号，没有国家代码
                    var fullNumber = parts[0].Trim();
                    return new PhoneNumberInfo
                    {
                        FullNumber = fullNumber,
                        CountryCode = _countryCode, // 使用默认国家代码
                        LocalNumber = fullNumber
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                LogService.Instance.LogError($"[手机API] 解析手机号码数据失败: {phoneData}, 错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 批量释放多个手机号码
        /// </summary>
        public async Task<(int SuccessCount, int FailCount, string Message)> ReleaseBatchPhoneNumbersAsync(List<string> phoneNumbers)
        {
            int successCount = 0;
            int failCount = 0;
            var messages = new List<string>();

            LogService.Instance.LogInfo($"[手机API] 开始批量释放{phoneNumbers.Count}个手机号码");

            foreach (var phoneNumber in phoneNumbers)
            {
                try
                {
                    var result = await ReleasePhoneNumberAsync(phoneNumber);
                    if (result.Success)
                    {
                        successCount++;
                    }
                    else
                    {
                        failCount++;
                        messages.Add($"{phoneNumber}: {result.Message}");
                    }

                    // 每个释放请求之间等待500ms，避免API频率限制
                    await Task.Delay(500);
                }
                catch (Exception ex)
                {
                    failCount++;
                    messages.Add($"{phoneNumber}: 异常 - {ex.Message}");
                }
            }

            var summary = $"批量释放完成: 成功{successCount}个, 失败{failCount}个";
            if (messages.Count > 0)
            {
                summary += $"\n失败详情: {string.Join("; ", messages)}";
            }

            LogService.Instance.LogInfo($"[手机API] {summary}");
            return (successCount, failCount, summary);
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
