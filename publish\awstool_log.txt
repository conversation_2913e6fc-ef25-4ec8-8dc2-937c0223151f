2025-08-01 11:37:11 [信息] AWS自动注册工具启动
2025-08-01 11:37:11 [信息] 程序版本: 1.0.0.0
2025-08-01 11:37:11 [信息] 启动时间: 2025-08-01 11:37:11
2025-08-01 11:37:11 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-01 11:37:11 [信息] 线程数量已选择: 1
2025-08-01 11:37:11 [信息] 线程数量选择初始化完成
2025-08-01 11:37:11 [信息] 程序初始化完成
2025-08-01 11:37:13 [按钮操作] 测试浏览器 -> 测试浏览器启动 - 模式: LocalChromeIncognito
2025-08-01 11:37:13 [系统状态] 正在检测Chrome浏览器(无痕模式)...
2025-08-01 11:37:14 [系统状态] 使用默认浏览器语言: English (United States)
2025-08-01 11:37:14 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 11:37:14 [信息] 单线程模式窗口布局: 使用固定位置(0, 0), 大小(600x400)
2025-08-01 11:37:14 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-01 11:37:14 [系统状态] 尝试启动系统Chrome(无痕模式)...
2025-08-01 11:37:15 [系统状态] 获取无痕模式页面...
2025-08-01 11:37:18 [系统状态] 创建新的无痕模式页面
2025-08-01 11:37:18 [系统状态] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用
2025-08-01 11:37:18 [信息] 无痕Chrome浏览器测试成功
2025-08-01 11:37:25 [信息] 程序正在退出，开始清理工作...
2025-08-01 11:37:25 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-01 11:37:25 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-01 11:37:25 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-01 11:37:25 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-01 11:37:25 [信息] 程序退出清理工作完成
2025-08-01 11:54:36 [信息] AWS自动注册工具启动
2025-08-01 11:54:36 [信息] 程序版本: 1.0.0.0
2025-08-01 11:54:36 [信息] 启动时间: 2025-08-01 11:54:36
2025-08-01 11:54:36 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-01 11:54:36 [信息] 线程数量已选择: 1
2025-08-01 11:54:36 [信息] 线程数量选择初始化完成
2025-08-01 11:54:36 [信息] 程序初始化完成
2025-08-01 11:54:38 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-01 11:54:39 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-07-31-阿塞拜疆.txt
2025-08-01 11:54:40 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-07-31-阿塞拜疆.txt
2025-08-01 11:54:40 [信息] 成功加载 12 条数据
2025-08-01 11:54:42 [信息] 线程数量已选择: 3
2025-08-01 11:54:45 [按钮操作] 开始注册 -> 启动注册流程
2025-08-01 11:54:45 [信息] 开始启动多线程注册，线程数量: 3
2025-08-01 11:54:45 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 12
2025-08-01 11:54:45 [信息] 所有线程已停止并清理
2025-08-01 11:54:45 [信息] 正在初始化多线程服务...
2025-08-01 11:54:45 [信息] 美国手机API服务已初始化
2025-08-01 11:54:45 [信息] 手机号码管理器已初始化，服务商: USA，将在第一个线程完成第二页后获取手机号码
2025-08-01 11:54:45 [信息] 多线程服务初始化完成
2025-08-01 11:54:45 [信息] 数据分配完成：共12条数据分配给3个线程
2025-08-01 11:54:45 [信息] 线程1分配到4条数据
2025-08-01 11:54:45 [信息] 线程2分配到4条数据
2025-08-01 11:54:45 [信息] 线程3分配到4条数据
2025-08-01 11:54:45 [信息] 屏幕工作区域: 1280x672
2025-08-01 11:54:45 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 11:54:45 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-01 11:54:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 [信息] 线程1已创建，窗口位置: (0, 0)
2025-08-01 11:54:45 [信息] 屏幕工作区域: 1280x672
2025-08-01 11:54:45 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 11:54:45 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-01 11:54:45 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 [信息] 线程2已创建，窗口位置: (0, 219)
2025-08-01 11:54:45 [信息] 屏幕工作区域: 1280x672
2025-08-01 11:54:45 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 11:54:45 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-01 11:54:45 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 [信息] 线程3已创建，窗口位置: (0, 438)
2025-08-01 11:54:45 [信息] 多线程注册启动成功，共3个线程
2025-08-01 11:54:45 线程1：[信息] 开始启动注册流程
2025-08-01 11:54:45 线程2：[信息] 开始启动注册流程
2025-08-01 11:54:45 线程3：[信息] 开始启动注册流程
2025-08-01 11:54:45 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-01 11:54:45 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 11:54:45 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-01 11:54:45 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-01 11:54:45 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-01 11:54:45 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-01 11:54:45 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 11:54:45 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 11:54:45 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 11:54:45 [信息] 多线程管理窗口已初始化
2025-08-01 11:54:45 [信息] UniformGrid列数已更新为: 1
2025-08-01 11:54:45 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-01 11:54:45 [信息] 多线程管理窗口已打开
2025-08-01 11:54:45 [信息] 多线程注册启动成功，共3个线程
2025-08-01 11:54:49 [信息] UniformGrid列数已更新为: 1
2025-08-01 11:54:49 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-01 11:54:49 线程2：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 11:54:49 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 11:54:49 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-01 11:54:49 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 11:54:50 [信息] UniformGrid列数已更新为: 1
2025-08-01 11:54:50 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-01 11:54:50 线程1：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 11:54:50 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 11:54:50 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-01 11:54:50 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 11:54:50 [信息] UniformGrid列数已更新为: 2
2025-08-01 11:54:50 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-01 11:54:50 线程3：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 11:54:50 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 11:54:50 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-01 11:54:50 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 11:54:51 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 11:54:51 线程3：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 11:54:51 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 11:54:54 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 11:54:55 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-08-01 11:54:55 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 11:54:55 线程2：[信息] 浏览器启动成功
2025-08-01 11:54:55 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-01 11:54:55 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-01 11:54:55 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 11:54:55 线程3：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 11:54:55 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 11:54:55 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 11:54:55 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 11:54:55 线程2：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 11:54:55 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-01 11:54:55 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 11:54:55 线程2：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 11:54:55 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 11:54:55 线程2：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 11:54:55 线程2：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 11:54:55 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 11:54:56 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 0%)
2025-08-01 11:54:56 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 11:54:56 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-08-01 11:54:56 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 11:54:56 线程1：[信息] 浏览器启动成功
2025-08-01 11:54:56 线程3：[信息] 浏览器启动成功
2025-08-01 11:54:56 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-01 11:54:56 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-01 11:54:56 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-01 11:54:56 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-01 11:54:56 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 11:54:56 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 11:54:56 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 11:54:56 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 11:54:56 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 11:54:56 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 11:54:56 线程3：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 11:54:56 线程1：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 11:54:56 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-01 11:54:56 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-01 11:54:56 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 11:54:56 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 11:54:56 线程3：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 11:54:56 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 11:54:56 线程1：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 11:54:56 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 11:54:56 线程3：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0... (进度: 98%)
2025-08-01 11:54:56 线程3：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 11:54:56 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 11:54:56 线程1：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-08-01 11:54:56 线程1：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 11:54:56 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 11:54:57 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-01 11:54:57 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-01 11:54:57 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 11:54:57 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 11:54:57 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 11:54:57 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 11:54:57 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-01 11:54:57 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-01 11:54:57 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 11:54:57 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 11:54:57 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-08-01 11:54:57 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 11:54:58 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-01 11:54:58 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-01 11:54:58 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 11:54:58 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 11:54:58 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0... (进度: 98%)
2025-08-01 11:54:58 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 11:55:11 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-01 11:55:11 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 11:55:11 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 11:55:11 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 11:55:11 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 11:55:11 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 11:55:14 线程3：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程3 - AWS注册 (进度: 98%)
2025-08-01 11:55:14 线程3：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 11:55:14 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 11:55:14 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 11:55:14 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 11:55:14 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-01 11:55:14 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 11:55:14 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 11:55:14 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 11:55:14 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 11:55:14 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 11:55:14 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 11:55:14 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:55:14 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 11:55:17 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 11:55:17 线程3：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 11:55:17 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 11:55:17 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 11:55:17 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 11:55:17 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-01 11:55:17 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 11:55:17 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 11:55:17 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 11:55:17 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 11:55:18 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 11:55:18 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-01 11:55:18 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 1781 字节 (进度: 100%)
2025-08-01 11:55:18 线程2：[信息] [信息] ❌ 图片宽度 67px 不符合验证码特征（应为100-300px） (进度: 100%)
2025-08-01 11:55:18 线程2：[信息] [信息] 第一页捕获的图片不符合验证码特征，跳过此次尝试（不计入重试次数） (进度: 100%)
2025-08-01 11:55:19 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-01 11:55:19 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 11:55:19 线程3：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-01 11:55:19 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 11:55:19 线程3：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 11:55:19 线程3：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 11:55:19 线程3：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 11:55:20 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 11:55:20 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 11:55:20 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-01 11:55:20 线程2：[信息] [信息] 第一页图片验证失败，第1次图片不符合验证码特征 (进度: 100%)
2025-08-01 11:55:20 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:55:22 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 11:55:22 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 11:55:22
2025-08-01 11:55:23 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 1781 字节 (进度: 100%)
2025-08-01 11:55:23 线程2：[信息] [信息] ❌ 图片宽度 67px 不符合验证码特征（应为100-300px） (进度: 100%)
2025-08-01 11:55:23 线程2：[信息] [信息] 第一页捕获的图片不符合验证码特征，跳过此次尝试（不计入重试次数） (进度: 100%)
2025-08-01 11:55:25 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 11:55:25 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 11:55:25
2025-08-01 11:55:25 线程2：[信息] [信息] 第一页图片验证失败，第2次图片不符合验证码特征 (进度: 100%)
2025-08-01 11:55:25 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:55:28 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-01 11:55:28 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 11:55:28
2025-08-01 11:55:28 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 1781 字节 (进度: 100%)
2025-08-01 11:55:28 线程2：[信息] [信息] ❌ 图片宽度 67px 不符合验证码特征（应为100-300px） (进度: 100%)
2025-08-01 11:55:28 线程2：[信息] [信息] 第一页捕获的图片不符合验证码特征，跳过此次尝试（不计入重试次数） (进度: 100%)
2025-08-01 11:55:30 线程2：[信息] [信息] 第一页图片验证失败，第3次图片不符合验证码特征 (进度: 100%)
2025-08-01 11:55:30 线程2：[信息] [信息] 第一页连续3次图片不符合验证码特征，转为手动模式 (进度: 100%)
2025-08-01 11:55:30 线程2：[信息] [信息] 第一页图形验证码自动处理失败，转为手动模式 (进度: 100%)
2025-08-01 11:55:31 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-08-01 11:55:31 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 11:55:31
2025-08-01 11:55:32 线程1：[信息] [信息] 所有自动线程已停止 (进度: 5%)
2025-08-01 11:55:32 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 5%)
2025-08-01 11:55:32 线程1：[信息] 已暂停
2025-08-01 11:55:32 [信息] 线程1已暂停
2025-08-01 11:55:32 [信息] 线程1已暂停
2025-08-01 11:55:32 线程3：[信息] [信息] 第一页验证码元素等待超时，转为手动模式 (进度: 100%)
2025-08-01 11:55:32 线程3：[信息] [信息] 第一页验证码元素等待超时，直接转为手动模式 (进度: 100%)
2025-08-01 11:55:32 线程3：[信息] [信息] 第一页图形验证码自动处理失败，转为手动模式 (进度: 100%)
2025-08-01 11:55:34 [信息] [线程1] 第5次触发邮箱验证码获取...（最多20次）
2025-08-01 11:55:34 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 11:55:34
2025-08-01 11:55:36 [信息] 多线程窗口引用已清理
2025-08-01 11:55:36 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-01 11:55:36 [信息] 多线程管理窗口正在关闭
2025-08-01 11:55:37 [信息] 程序正在退出，开始清理工作...
2025-08-01 11:55:37 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-01 11:55:37 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-01 11:55:37 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-01 11:55:37 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-01 11:55:37 [信息] 程序退出清理工作完成
2025-08-01 12:01:05 [信息] AWS自动注册工具启动
2025-08-01 12:01:05 [信息] 程序版本: 1.0.0.0
2025-08-01 12:01:05 [信息] 启动时间: 2025-08-01 12:01:05
2025-08-01 12:01:05 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-01 12:01:05 [信息] 线程数量已选择: 1
2025-08-01 12:01:05 [信息] 线程数量选择初始化完成
2025-08-01 12:01:05 [信息] 程序初始化完成
2025-08-01 12:02:32 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-01 12:02:33 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-07-31-阿塞拜疆.txt
2025-08-01 12:02:34 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-07-31-阿塞拜疆.txt
2025-08-01 12:02:34 [信息] 成功加载 12 条数据
2025-08-01 12:02:35 [信息] 线程数量已选择: 2
2025-08-01 12:02:39 [按钮操作] 开始注册 -> 启动注册流程
2025-08-01 12:02:39 [信息] 开始启动多线程注册，线程数量: 2
2025-08-01 12:02:39 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 12
2025-08-01 12:02:39 [信息] 所有线程已停止并清理
2025-08-01 12:02:39 [信息] 正在初始化多线程服务...
2025-08-01 12:02:39 [信息] 美国手机API服务已初始化
2025-08-01 12:02:39 [信息] 手机号码管理器已初始化，服务商: USA，将在第一个线程完成第二页后获取手机号码
2025-08-01 12:02:39 [信息] 多线程服务初始化完成
2025-08-01 12:02:39 [信息] 数据分配完成：共12条数据分配给2个线程
2025-08-01 12:02:39 [信息] 线程1分配到6条数据
2025-08-01 12:02:39 [信息] 线程2分配到6条数据
2025-08-01 12:02:39 [信息] 屏幕工作区域: 1280x672
2025-08-01 12:02:39 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 12:02:39 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-01 12:02:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 [信息] 线程1已创建，窗口位置: (0, 0)
2025-08-01 12:02:39 [信息] 屏幕工作区域: 1280x672
2025-08-01 12:02:39 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 12:02:39 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-01 12:02:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 [信息] 线程2已创建，窗口位置: (0, 329)
2025-08-01 12:02:39 [信息] 多线程注册启动成功，共2个线程
2025-08-01 12:02:39 线程1：[信息] 开始启动注册流程
2025-08-01 12:02:39 线程2：[信息] 开始启动注册流程
2025-08-01 12:02:39 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 12:02:39 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-01 12:02:39 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-01 12:02:39 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-01 12:02:39 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 12:02:39 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 12:02:39 [信息] 多线程管理窗口已初始化
2025-08-01 12:02:39 [信息] UniformGrid列数已更新为: 1
2025-08-01 12:02:39 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-01 12:02:39 [信息] 多线程管理窗口已打开
2025-08-01 12:02:39 [信息] 多线程注册启动成功，共2个线程
2025-08-01 12:02:41 [信息] UniformGrid列数已更新为: 1
2025-08-01 12:02:41 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-01 12:02:41 线程1：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 12:02:41 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 12:02:41 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-01 12:02:41 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 12:02:41 [信息] UniformGrid列数已更新为: 1
2025-08-01 12:02:41 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-01 12:02:41 线程2：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 12:02:41 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 12:02:41 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-01 12:02:41 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 12:02:42 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 12:02:43 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 12:02:46 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 12:02:48 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 12:02:48 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-08-01 12:02:48 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 12:02:48 线程2：[信息] 浏览器启动成功
2025-08-01 12:02:48 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-01 12:02:48 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-01 12:02:48 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 12:02:48 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 12:02:48 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 12:02:48 线程2：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 12:02:48 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-08-01 12:02:48 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 12:02:48 线程1：[信息] 浏览器启动成功
2025-08-01 12:02:48 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-01 12:02:48 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-01 12:02:48 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 12:02:48 线程2：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 12:02:48 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 12:02:48 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-01 12:02:48 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 12:02:48 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 12:02:48 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 12:02:48 线程1：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 12:02:48 线程2：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:02:49 线程2：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 12:02:49 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 12:02:49 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 12:02:49 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 12:02:49 线程1：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 12:02:49 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 12:02:49 线程1：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:02:49 线程1：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 12:02:49 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 12:02:50 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-01 12:02:50 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-01 12:02:50 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 12:02:50 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 12:02:50 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:02:50 线程2：[信息] [信息] 正在新建标签页打开AWS注册页面... (进度: 98%)
2025-08-01 12:02:51 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-01 12:02:51 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-01 12:02:51 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 12:02:52 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 12:02:52 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:02:52 线程1：[信息] [信息] 正在新建标签页打开AWS注册页面... (进度: 98%)
2025-08-01 12:03:04 线程2：[信息] [信息] AWS注册页面已在新标签页中打开，测试页面保持显示线程号 (进度: 98%)
2025-08-01 12:03:04 线程1：[信息] [信息] AWS注册页面已在新标签页中打开，测试页面保持显示线程号 (进度: 98%)
2025-08-01 12:03:06 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-01 12:03:06 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 12:03:06 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 12:03:06 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 12:03:06 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 12:03:09 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-01 12:03:09 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 12:03:09 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 12:03:09 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 12:03:09 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 12:03:10 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 12:03:10 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 12:03:12 [信息] 多线程窗口引用已清理
2025-08-01 12:03:12 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-01 12:03:12 [信息] 多线程管理窗口正在关闭
2025-08-01 12:03:13 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 12:03:13 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 12:03:13 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] [信息] 检查IP异常错误时出现异常: Target page, context or browser has been closed (进度: 98%)
2025-08-01 12:03:13 [信息] 检查IP异常错误时出现异常: Target page, context or browser has been closed
2025-08-01 12:03:13 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] [信息] ❌ 第一页图形验证码处理异常: Target page, context or browser has been closed (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] [信息] ❌ 异常详情:    at Microsoft.Playwright.Transport.Connection.InnerSendMessageToServerAsync[T](ChannelOwnerBase object, String method, Dictionary`2 dictionary, Boolean keepNulls) in /_/src/Playwright/Transport/Connection.cs:line 209
   at Microsoft.Playwright.Transport.Connection.WrapApiCallAsync[T](Func`1 action, Boolean isInternal) in /_/src/Playwright/Transport/Connection.cs:line 535
   at Microsoft.Playwright.Transport.Channels.FrameChannel.QueryCountAsync(String selector) in /_/src/Playwright/Transport/Channels/FrameChannel.cs:line 262
   at AWSAutoRegister.Services.AutomationService.CheckForCaptchaOnPage1() in C:\Users\<USER>\Desktop\AGaws单线程版-备份\Services\AutomationService.cs:line 12013 (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] [信息] 等待验证码页面超时 (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 12:03:13 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 12:03:14 [信息] 程序正在退出，开始清理工作...
2025-08-01 12:03:14 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-01 12:03:14 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-01 12:03:14 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-01 12:03:14 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-01 12:03:14 [信息] 程序退出清理工作完成
2025-08-01 12:10:58 [信息] AWS自动注册工具启动
2025-08-01 12:10:58 [信息] 程序版本: 1.0.0.0
2025-08-01 12:10:58 [信息] 启动时间: 2025-08-01 12:10:58
2025-08-01 12:10:58 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-01 12:10:58 [信息] 线程数量已选择: 1
2025-08-01 12:10:58 [信息] 线程数量选择初始化完成
2025-08-01 12:10:58 [信息] 程序初始化完成
2025-08-01 12:11:03 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-01 12:11:05 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-07-31-阿塞拜疆.txt
2025-08-01 12:11:05 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-07-31-阿塞拜疆.txt
2025-08-01 12:11:06 [信息] 成功加载 12 条数据
2025-08-01 12:11:08 [信息] 线程数量已选择: 2
2025-08-01 12:11:12 [按钮操作] 开始注册 -> 启动注册流程
2025-08-01 12:11:12 [信息] 开始启动多线程注册，线程数量: 2
2025-08-01 12:11:12 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 12
2025-08-01 12:11:12 [信息] 所有线程已停止并清理
2025-08-01 12:11:12 [信息] 正在初始化多线程服务...
2025-08-01 12:11:12 [信息] 美国手机API服务已初始化
2025-08-01 12:11:12 [信息] 手机号码管理器已初始化，服务商: USA，将在第一个线程完成第二页后获取手机号码
2025-08-01 12:11:12 [信息] 多线程服务初始化完成
2025-08-01 12:11:12 [信息] 数据分配完成：共12条数据分配给2个线程
2025-08-01 12:11:12 [信息] 线程1分配到6条数据
2025-08-01 12:11:12 [信息] 线程2分配到6条数据
2025-08-01 12:11:12 [信息] 屏幕工作区域: 1280x672
2025-08-01 12:11:12 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 12:11:12 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-01 12:11:12 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 [信息] 线程1已创建，窗口位置: (0, 0)
2025-08-01 12:11:12 [信息] 屏幕工作区域: 1280x672
2025-08-01 12:11:12 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 12:11:12 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-01 12:11:12 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 [信息] 线程2已创建，窗口位置: (0, 329)
2025-08-01 12:11:12 [信息] 多线程注册启动成功，共2个线程
2025-08-01 12:11:12 线程2：[信息] 开始启动注册流程
2025-08-01 12:11:12 线程1：[信息] 开始启动注册流程
2025-08-01 12:11:12 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 12:11:12 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 12:11:12 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-01 12:11:12 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-01 12:11:12 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 12:11:12 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 12:11:12 [信息] 多线程管理窗口已初始化
2025-08-01 12:11:12 [信息] UniformGrid列数已更新为: 1
2025-08-01 12:11:12 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-01 12:11:12 [信息] 多线程管理窗口已打开
2025-08-01 12:11:12 [信息] 多线程注册启动成功，共2个线程
2025-08-01 12:11:14 [信息] UniformGrid列数已更新为: 1
2025-08-01 12:11:14 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-01 12:11:14 线程2：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 12:11:14 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 12:11:14 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-08-01 12:11:14 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 12:11:14 [信息] UniformGrid列数已更新为: 1
2025-08-01 12:11:14 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-01 12:11:14 线程1：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 12:11:14 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 12:11:14 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0
2025-08-01 12:11:14 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 12:11:15 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 12:11:15 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 12:11:22 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 12:11:22 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 12:11:23 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-08-01 12:11:23 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 12:11:23 线程2：[信息] 浏览器启动成功
2025-08-01 12:11:23 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-01 12:11:23 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-01 12:11:23 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 12:11:23 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 12:11:23 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 12:11:23 线程2：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 12:11:23 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-08-01 12:11:23 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 12:11:23 线程1：[信息] 浏览器启动成功
2025-08-01 12:11:23 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-01 12:11:23 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-01 12:11:23 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 12:11:23 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-01 12:11:23 线程2：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 12:11:23 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 12:11:23 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 12:11:23 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 12:11:23 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 12:11:23 线程1：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 12:11:23 线程2：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:11:23 线程2：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 12:11:23 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 12:11:23 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-01 12:11:23 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 12:11:23 线程1：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 12:11:23 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 12:11:23 线程1：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:11:23 线程1：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 12:11:23 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 12:11:24 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-01 12:11:24 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-01 12:11:24 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 12:11:24 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 12:11:24 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:11:24 线程2：[信息] [信息] 正在新建标签页打开AWS注册页面... (进度: 98%)
2025-08-01 12:11:25 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-01 12:11:25 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-01 12:11:25 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 12:11:25 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 12:11:25 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:11:25 线程1：[信息] [信息] 正在新建标签页打开AWS注册页面... (进度: 98%)
2025-08-01 12:11:37 线程2：[信息] [信息] AWS注册页面已在新标签页中打开，测试页面保持显示线程号 (进度: 98%)
2025-08-01 12:11:37 线程1：[信息] [信息] AWS注册页面已在新标签页中打开，测试页面保持显示线程号 (进度: 98%)
2025-08-01 12:11:38 线程2：[信息] [信息] 注册失败: Execution context was destroyed, most likely because of a navigation (进度: 98%)
2025-08-01 12:11:38 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 98%)
2025-08-01 12:11:38 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-01 12:11:38 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 12:11:38 线程1：[信息] [信息] 注册失败: Execution context was destroyed, most likely because of a navigation (进度: 98%)
2025-08-01 12:11:38 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:11:38 [信息] 多线程状态已重置
2025-08-01 12:11:38 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:11:38 [信息] 多线程状态已重置
2025-08-01 12:11:38 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 98%)
2025-08-01 12:11:38 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:11:38 [信息] 多线程状态已重置
2025-08-01 12:11:38 线程1：[错误] 账户注册启动失败: <EMAIL>
2025-08-01 12:11:38 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 12:12:12 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:12 [信息] 多线程状态已重置
2025-08-01 12:12:12 线程1：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-01 12:12:12 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:12 [信息] 多线程状态已重置
2025-08-01 12:12:12 线程1：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-01 12:12:12 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:12 [信息] 多线程状态已重置
2025-08-01 12:12:12 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-01 12:12:12 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:12 [信息] 多线程状态已重置
2025-08-01 12:12:12 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程2：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程2：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程1：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-01 12:12:14 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:14 [信息] 多线程状态已重置
2025-08-01 12:12:14 线程1：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-01 12:12:14 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-01 12:12:14 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-01 12:12:14 线程2：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-01 12:12:14 线程2：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-01 12:12:14 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-01 12:12:14 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-01 12:12:15 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-01 12:12:15 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-01 12:12:15 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-01 12:12:15 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-01 12:12:15 线程2：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-01 12:12:15 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-01 12:12:15 线程1：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-01 12:12:15 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-01 12:12:15 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-01 12:12:15 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 12:12:15 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-01 12:12:15 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 12:12:15 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-01 12:12:15 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-01 12:12:18 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-01 12:12:18 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 12:12:22 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 29379 字节 (进度: 100%)
2025-08-01 12:12:22 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，29379字节，复杂度符合要求 (进度: 100%)
2025-08-01 12:12:22 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 12:12:23 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 29664 字节 (进度: 100%)
2025-08-01 12:12:23 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，29664字节，复杂度符合要求 (进度: 100%)
2025-08-01 12:12:23 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 12:12:24 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"6ggsgf"},"taskId":"b9f6e3d2-6e8d-11f0-9ac6-5ee226f458ac"} (进度: 100%)
2025-08-01 12:12:24 线程2：[信息] [信息] 第一页第1次识别结果: 6ggsgf → 转换为小写: 6ggsgf (进度: 100%)
2025-08-01 12:12:24 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 12:12:24 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"d3ctp4"},"taskId":"ba004cb0-6e8d-11f0-af44-ba19b662e7c3"} (进度: 100%)
2025-08-01 12:12:24 线程1：[信息] [信息] 第一页第1次识别结果: d3ctp4 → 转换为小写: d3ctp4 (进度: 100%)
2025-08-01 12:12:24 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 12:12:24 线程2：[信息] [信息] 已填入验证码: 6ggsgf (进度: 100%)
2025-08-01 12:12:24 线程1：[信息] [信息] 已填入验证码: d3ctp4 (进度: 100%)
2025-08-01 12:12:24 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 12:12:24 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-01 12:12:26 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 12:12:26 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-01 12:12:26 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 12:12:26 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] 已继续
2025-08-01 12:12:26 [信息] 线程2已继续
2025-08-01 12:12:26 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 12:12:26 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 12:12:26 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 12:12:26 线程1：[信息] 已继续
2025-08-01 12:12:26 [信息] 线程1已继续
2025-08-01 12:12:26 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 12:12:26 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 12:12:26 [信息] [线程1] 已删除旧的请求文件
2025-08-01 12:12:26 [信息] [线程2] 已删除旧的响应文件
2025-08-01 12:12:26 [信息] [线程1] 已删除旧的响应文件
2025-08-01 12:12:26 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-01 12:12:26 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-01 12:12:28 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 12:12:28 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 12:12:28
2025-08-01 12:12:28 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 12:12:28 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 12:12:28
