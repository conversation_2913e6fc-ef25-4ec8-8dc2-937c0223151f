using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using AWSAutoRegister.Models;
using AWSAutoRegister.Services;

namespace AWSAutoRegister
{
    /// <summary>
    /// 多线程管理窗口
    /// </summary>
    public partial class MultiThreadWindow : Window
    {
        private readonly MultiThreadManager _multiThreadManager;
        private readonly ObservableCollection<ThreadViewModel> _threadViewModels = new ObservableCollection<ThreadViewModel>();
        private readonly LogService _logService = LogService.Instance;
        private MainWindow? _mainWindow;

        public MultiThreadWindow(MultiThreadManager multiThreadManager, MainWindow? mainWindow = null)
        {
            InitializeComponent();
            
            _multiThreadManager = multiThreadManager;
            _mainWindow = mainWindow;
            
            // 设置数据绑定
            ThreadListView.ItemsSource = _threadViewModels;
            
            // 订阅多线程管理器事件
            _multiThreadManager.ThreadStatusChanged += OnThreadStatusChanged;
            _multiThreadManager.ThreadManualActionRequired += OnThreadManualActionRequired;
            _multiThreadManager.ThreadDataCompleted += OnThreadDataCompleted;
            _multiThreadManager.ThreadDataStarted += OnThreadDataStarted;
            _multiThreadManager.OverallStatusChanged += OnOverallStatusChanged;
            _multiThreadManager.ThreadSaveClipboardInfo += OnThreadSaveClipboardInfo;
            _multiThreadManager.ThreadSaveFailedData += OnThreadSaveFailedData;
            _multiThreadManager.ThreadPage2Completed += OnThreadPage2Completed;

            // 添加窗口关闭事件处理
            this.Closing += MultiThreadWindow_Closing;

            // 添加窗口加载事件处理
            this.Loaded += MultiThreadWindow_Loaded;

            _logService.LogInfo("多线程管理窗口已初始化");
        }

        /// <summary>
        /// 窗口加载事件处理
        /// </summary>
        private void MultiThreadWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // 根据线程数量调整窗口大小和位置
            AdjustWindowSizeAndPosition();
        }

        /// <summary>
        /// 设置窗口固定尺寸和位置（与主窗口尺寸一致，位置在屏幕最右边）
        /// </summary>
        private void AdjustWindowSizeAndPosition()
        {
            try
            {
                // 获取屏幕工作区域
                var workingArea = SystemParameters.WorkArea;

                // 设置与主窗口完全一致的尺寸
                var mainWindowWidth = 650;  // 主窗口宽度
                var mainWindowHeight = 608; // 主窗口高度

                this.Width = mainWindowWidth;
                this.Height = mainWindowHeight;

                // 设置窗口位置到屏幕最右边
                this.Left = workingArea.Right - this.Width - 20; // 距离右边20像素
                this.Top = workingArea.Top + 50;   // 距离顶部50像素

                // 确保窗口不超出屏幕边界
                if (this.Left < workingArea.Left)
                {
                    this.Left = workingArea.Left + 20;
                }
                if (this.Top + this.Height > workingArea.Bottom)
                {
                    this.Top = workingArea.Bottom - this.Height - 20;
                }

                // 根据线程数量设置列数
                var threadCount = _threadViewModels.Count;
                int columns = threadCount <= 2 ? 1 : 2;
                UpdateUniformGridColumns(columns);

                _logService.LogInfo($"窗口已设置固定尺寸: {this.Width}x{this.Height}（与主窗口一致）, 最右边位置: ({this.Left}, {this.Top}), 线程数量: {threadCount}, 列数: {columns}");
            }
            catch (Exception ex)
            {
                _logService.LogError($"调整窗口大小失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 动态更新UniformGrid的列数
        /// </summary>
        private void UpdateUniformGridColumns(int columns)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    // 创建新的ItemsPanelTemplate
                    var factory = new FrameworkElementFactory(typeof(UniformGrid));
                    factory.SetValue(UniformGrid.ColumnsProperty, columns);

                    var template = new ItemsPanelTemplate(factory);
                    ThreadListView.ItemsPanel = template;

                    _logService.LogInfo($"UniformGrid列数已更新为: {columns}");
                });
            }
            catch (Exception ex)
            {
                _logService.LogError($"更新UniformGrid列数失败: {ex.Message}");
            }
        }



        /// <summary>
        /// 暂停所有可暂停的线程
        /// </summary>
        private async void PauseAllBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusText.Text = "正在暂停所有可暂停的线程...";

                var tasks = new List<Task>();
                int pausableCount = 0;

                foreach (var threadViewModel in _threadViewModels)
                {
                    // 只暂停可以暂停的线程（处理中、等待手动操作）
                    if (threadViewModel.IsPauseEnabled)
                    {
                        tasks.Add(_multiThreadManager.PauseThread(threadViewModel.ThreadId));
                        pausableCount++;
                    }
                }

                if (pausableCount > 0)
                {
                    await Task.WhenAll(tasks);
                    StatusText.Text = $"已暂停 {pausableCount} 个线程";
                    _logService.LogInfo($"暂停了 {pausableCount} 个可暂停的线程");
                }
                else
                {
                    StatusText.Text = "没有可暂停的线程";
                    _logService.LogInfo("没有找到可暂停的线程");
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = $"暂停失败: {ex.Message}";
                _logService.LogError($"暂停所有线程失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 继续所有可继续的线程（暂停状态和等待手动操作状态）
        /// </summary>
        private async void ResumeAllBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusText.Text = "正在继续所有可继续的线程...";

                var tasks = new List<Task>();
                int continuableCount = 0;

                foreach (var threadViewModel in _threadViewModels)
                {
                    // 继续所有可以继续的线程（暂停状态和等待手动操作状态）
                    if (threadViewModel.IsContinueEnabled)
                    {
                        tasks.Add(_multiThreadManager.ResumeThread(threadViewModel.ThreadId));
                        continuableCount++;
                    }
                }

                if (continuableCount > 0)
                {
                    await Task.WhenAll(tasks);
                    StatusText.Text = $"已继续 {continuableCount} 个线程";
                    _logService.LogInfo($"继续了 {continuableCount} 个可继续的线程");
                }
                else
                {
                    StatusText.Text = "没有可继续的线程";
                    _logService.LogInfo("没有找到可继续的线程");
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = $"继续失败: {ex.Message}";
                _logService.LogError($"继续所有线程失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 终止所有线程
        /// </summary>
        private async void TerminateAllBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("确定要终止所有未完成注册的线程吗？\n\n这将停止所有还没注册完成的线程，已完成注册的线程不会被影响。\n正在处理的数据将移动到已被终止注册数据列表。",
                    "确认终止", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    StatusText.Text = "正在终止所有线程...";
                    _logService.LogButtonClick("终止所有线程", "终止所有正在进行的注册流程");

                    // 先获取所有可终止线程的完整数据（在终止之前）
                    var terminatedDataList = new List<RegistrationData>();
                    var terminableThreads = new List<ThreadViewModel>();

                    _logService.LogInfo($"开始检查{_threadViewModels.Count}个线程的终止状态");

                    foreach (var threadViewModel in _threadViewModels)
                    {
                        // 根据用户要求：只终止还没注册完成的线程
                        // 注册完成包括：Completed（成功）、CompletedWithIssues（有问题但完成）、Terminated（已终止）
                        bool isRegistrationIncomplete = threadViewModel.Status.Status != ThreadStatus.Completed &&
                                                       threadViewModel.Status.Status != ThreadStatus.CompletedWithIssues &&
                                                       threadViewModel.Status.Status != ThreadStatus.Terminated;

                        if (isRegistrationIncomplete)
                        {
                            terminableThreads.Add(threadViewModel);
                            _logService.LogInfo($"线程{threadViewModel.ThreadId}注册未完成（状态: {threadViewModel.Status.Status}），将被终止");

                            // 从多线程管理器获取当前处理的完整数据
                            var currentData = _multiThreadManager.GetCurrentDataForThread(threadViewModel.ThreadId);
                            if (currentData != null)
                            {
                                terminatedDataList.Add(currentData);
                                _logService.LogInfo($"线程{threadViewModel.ThreadId}正在处理数据: {currentData.Email}");
                            }
                            else
                            {
                                _logService.LogInfo($"线程{threadViewModel.ThreadId}当前没有处理数据");
                            }
                        }
                        else
                        {
                            _logService.LogInfo($"线程{threadViewModel.ThreadId}注册已完成，跳过终止（状态: {threadViewModel.Status.Status}）");
                        }
                    }

                    if (terminableThreads.Count == 0)
                    {
                        StatusText.Text = "没有需要终止的线程（所有线程都已完成注册）";
                        _logService.LogInfo("没有找到需要终止的线程，所有线程都已完成注册");
                        return;
                    }

                    _logService.LogInfo($"总共找到{terminatedDataList.Count}个需要终止的数据: {string.Join(", ", terminatedDataList.Select(d => d.Email))}");

                    // 先处理数据移动，再终止线程
                    if (terminatedDataList.Count > 0)
                    {
                        // 通知主窗口处理数据移动
                        if (Owner is MainWindow mainWindow)
                        {
                            _logService.LogInfo($"通知主窗口处理{terminatedDataList.Count}个终止数据");
                            mainWindow.HandleTerminatedDataFromMultiThread(terminatedDataList);
                        }
                        else
                        {
                            _logService.LogError("无法获取主窗口引用，数据移动失败");
                        }
                    }
                    else
                    {
                        _logService.LogInfo("没有找到需要终止的数据");
                    }

                    // 只终止可终止的线程
                    var terminateTasks = new List<Task>();
                    foreach (var threadViewModel in terminableThreads)
                    {
                        terminateTasks.Add(_multiThreadManager.TerminateThread(threadViewModel.ThreadId));
                    }

                    await Task.WhenAll(terminateTasks);

                    StatusText.Text = $"已终止 {terminableThreads.Count} 个未完成注册的线程，{terminatedDataList.Count} 个数据已移动到终止列表";

                    // 从视图中移除已终止的线程
                    foreach (var terminatedThread in terminableThreads)
                    {
                        _threadViewModels.Remove(terminatedThread);
                    }
                    UpdateThreadCounts();

                    // 恢复主窗口按钮状态（通过主窗口处理）
                    if (Owner is MainWindow mainWindow2)
                    {
                        mainWindow2.RestoreButtonStateAfterMultiThreadTermination();
                    }
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = $"终止失败: {ex.Message}";
                _logService.LogError($"终止所有线程失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 返回主窗口
        /// </summary>
        private void BackToMainBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_mainWindow != null)
                {
                    _mainWindow.Show();
                    _mainWindow.WindowState = WindowState.Normal;
                    _mainWindow.Activate();
                }
                
                this.Hide();
                _logService.LogInfo("返回主窗口");
            }
            catch (Exception ex)
            {
                _logService.LogError($"返回主窗口失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 暂停单个线程
        /// </summary>
        private async void PauseThreadBtn_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int threadId)
            {
                try
                {
                    await _multiThreadManager.PauseThread(threadId);
                    _logService.LogInfo($"线程{threadId}已暂停");
                }
                catch (Exception ex)
                {
                    _logService.LogError($"暂停线程{threadId}失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 继续单个线程注册
        /// </summary>
        private async void ContinueThreadBtn_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int threadId)
            {
                try
                {
                    await _multiThreadManager.ResumeThread(threadId);
                    // 不记录"已继续"日志，让AutomationService显示具体状态
                }
                catch (Exception ex)
                {
                    _logService.LogError($"继续线程{threadId}失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 处理单个线程的手动操作
        /// </summary>
        private void HandleThreadBtn_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int threadId)
            {
                try
                {
                    // 激活对应的浏览器窗口
                    // 这里需要实现浏览器窗口激活逻辑
                    StatusText.Text = $"请在浏览器中完成线程{threadId}的手动操作";
                    _logService.LogInfo($"用户开始处理线程{threadId}的手动操作");
                }
                catch (Exception ex)
                {
                    _logService.LogError($"处理线程{threadId}手动操作失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 终止单个线程
        /// </summary>
        private async void TerminateThreadBtn_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int threadId)
            {
                try
                {
                    var result = MessageBox.Show($"确定要终止线程{threadId}吗？", 
                        "确认终止", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                    
                    if (result == MessageBoxResult.Yes)
                    {
                        // 先获取当前线程处理的数据
                        var currentData = _multiThreadManager.GetCurrentDataForThread(threadId);

                        // 终止线程
                        await _multiThreadManager.TerminateThread(threadId);

                        // 处理数据移动（单个线程终止）
                        if (currentData != null)
                        {
                            var terminatedDataList = new List<RegistrationData> { currentData };
                            if (_mainWindow != null)
                            {
                                _mainWindow.HandleSingleThreadTerminatedData(threadId, terminatedDataList);
                            }
                        }

                        // 从视图中移除
                        var threadViewModel = _threadViewModels.FirstOrDefault(t => t.ThreadId == threadId);
                        if (threadViewModel != null)
                        {
                            _threadViewModels.Remove(threadViewModel);
                            UpdateThreadCounts();
                            AdjustWindowSizeAndPosition(); // 调整窗口大小
                        }

                        _logService.LogInfo($"线程{threadId}已终止");
                    }
                }
                catch (Exception ex)
                {
                    _logService.LogError($"终止线程{threadId}失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 线程状态变化事件处理
        /// </summary>
        private void OnThreadStatusChanged(int threadId, ThreadStatusInfo status)
        {
            Dispatcher.Invoke(() =>
            {
                var threadViewModel = _threadViewModels.FirstOrDefault(t => t.ThreadId == threadId);
                if (threadViewModel == null)
                {
                    // 创建新的线程视图模型
                    threadViewModel = new ThreadViewModel { ThreadId = threadId };

                    // 按照线程ID顺序插入，确保从左到右排列
                    int insertIndex = 0;
                    for (int i = 0; i < _threadViewModels.Count; i++)
                    {
                        if (_threadViewModels[i].ThreadId > threadId)
                        {
                            insertIndex = i;
                            break;
                        }
                        insertIndex = i + 1;
                    }

                    _threadViewModels.Insert(insertIndex, threadViewModel);
                    UpdateThreadCounts();

                    // 当有新线程添加时，调整窗口大小
                    AdjustWindowSizeAndPosition();
                }

                threadViewModel.Status = status;
                UpdateOverallProgress();
            });
        }

        /// <summary>
        /// 线程手动操作需求事件处理
        /// </summary>
        private void OnThreadManualActionRequired(int threadId, string message)
        {
            Dispatcher.Invoke(() =>
            {
                StatusText.Text = $"线程{threadId}需要手动操作: {message}";
                
                // 可以在这里添加更多的提醒逻辑，比如闪烁窗口等
                this.Activate();
            });
        }

        /// <summary>
        /// 线程数据开始处理事件处理
        /// </summary>
        private void OnThreadDataStarted(int threadId, RegistrationData data)
        {
            Dispatcher.Invoke(() =>
            {
                StatusText.Text = $"线程{threadId}开始处理数据: {data.Email}";

                // 更新线程视图模型的当前邮箱
                var threadViewModel = _threadViewModels.FirstOrDefault(t => t.ThreadId == threadId);
                if (threadViewModel != null)
                {
                    threadViewModel.CurrentEmail = data.Email;
                }

                // 通知主窗口更新数据状态为"处理中"
                _mainWindow?.UpdateDataStatusFromThread(data, Models.DataProcessStatus.Processing, $"线程{threadId}处理中");
            });
        }

        /// <summary>
        /// 线程数据完成事件处理
        /// </summary>
        private void OnThreadDataCompleted(int threadId, RegistrationData data)
        {
            Dispatcher.Invoke(async () =>
            {
                StatusText.Text = $"线程{threadId}正在完成数据处理: {data.Email}";

                // 清空线程视图模型的当前邮箱
                var threadViewModel = _threadViewModels.FirstOrDefault(t => t.ThreadId == threadId);
                if (threadViewModel != null)
                {
                    threadViewModel.CurrentEmail = string.Empty;
                }

                try
                {
                    // 获取对应的线程以确定完成类型
                    var registrationThread = _multiThreadManager?.GetRegistrationThread(threadId);
                    if (registrationThread != null)
                    {
                        // 根据线程的完成类型调用不同的主窗口方法
                        var completionType = GetCompletionTypeFromThread(registrationThread);

                        // 等待主窗口完成所有操作
                        await Task.Run(() => {
                            switch (completionType)
                            {
                                case "WithBillingIssue":
                                    _mainWindow?.OnDataCompletedWithBillingIssue(data);
                                    break;
                                case "WithIneligibleIssue":
                                    _mainWindow?.OnDataCompletedWithIneligibleIssue(data);
                                    break;
                                default:
                                    _mainWindow?.OnDataCompletedWithoutKeys(data);
                                    break;
                            }
                        });

                        // 最终完成线程
                        registrationThread.FinalizeCompletion(data);
                    }
                    else
                    {
                        // 如果无法获取线程，使用默认处理
                        await Task.Run(() => _mainWindow?.OnDataCompletedWithoutKeys(data));
                    }

                    StatusText.Text = $"线程{threadId}完成数据处理: {data.Email}";
                    UpdateOverallProgress();

                    _logService.LogInfo($"线程{threadId}数据完成事件已通知主窗口并最终完成: {data.Email}");
                }
                catch (Exception ex)
                {
                    _logService.LogError($"线程{threadId}完成数据处理时发生错误: {ex.Message}");
                    StatusText.Text = $"线程{threadId}处理数据时发生错误: {data.Email}";
                }
            });
        }

        /// <summary>
        /// 从线程获取完成类型
        /// </summary>
        private string GetCompletionTypeFromThread(RegistrationThread thread)
        {
            try
            {
                return thread.GetCompletionType();
            }
            catch (Exception ex)
            {
                _logService.LogWarning($"无法获取线程完成类型: {ex.Message}");
                return "WithoutKeys";
            }
        }

        /// <summary>
        /// 整体状态变化事件处理
        /// </summary>
        private void OnOverallStatusChanged(string status)
        {
            Dispatcher.Invoke(() =>
            {
                StatusText.Text = status;
            });
        }

        /// <summary>
        /// 线程剪贴板保存事件处理
        /// </summary>
        private void OnThreadSaveClipboardInfo(int threadId, string clipboardContent)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    // 通知主窗口保存剪贴板信息到"成功数据："区域
                    _mainWindow?.OnSaveClipboardInfoToSuccessData(clipboardContent);
                    _logService.LogInfo($"线程{threadId}剪贴板信息已保存: {clipboardContent}");
                });
            }
            catch (Exception ex)
            {
                _logService.LogError($"处理线程{threadId}剪贴板保存事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 线程失败数据保存事件处理
        /// </summary>
        private void OnThreadSaveFailedData(int threadId, string failureReason, RegistrationData? failedData)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    // 通知主窗口保存失败数据到"注册失败："区域
                    _mainWindow?.OnSaveFailedData(failureReason, failedData);
                    _logService.LogInfo($"线程{threadId}失败数据已保存: {failureReason}, 数据: {failedData?.Email}");
                });
            }
            catch (Exception ex)
            {
                _logService.LogError($"处理线程{threadId}失败数据保存事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 线程第二页完成事件处理
        /// </summary>
        private void OnThreadPage2Completed(int threadId)
        {
            Dispatcher.Invoke(() =>
            {
                StatusText.Text = $"线程{threadId}完成第二页，触发手机号码获取";
                _logService.LogInfo($"线程{threadId}完成第二页事件已处理");
            });
        }

        /// <summary>
        /// 更新总体进度
        /// </summary>
        private void UpdateOverallProgress()
        {
            if (_threadViewModels.Count == 0)
            {
                OverallProgress.Value = 0;
                OverallProgressText.Text = "0%";
                return;
            }

            var totalProgress = _threadViewModels.Sum(t => t.Status.Progress);
            var averageProgress = totalProgress / _threadViewModels.Count;

            OverallProgress.Value = averageProgress;
            OverallProgressText.Text = $"{averageProgress:F0}%";

            // 检查是否所有线程都已完成
            var allCompleted = _threadViewModels.All(t =>
                t.Status.Status == ThreadStatus.Completed ||
                t.Status.Status == ThreadStatus.CompletedWithIssues ||
                t.Status.Status == ThreadStatus.Failed ||
                t.Status.Status == ThreadStatus.Terminated);

            if (allCompleted && _threadViewModels.Count > 0)
            {
                _logService.LogInfo("所有线程已完成，通知主窗口重置状态");
                StatusText.Text = "所有线程已完成";

                // 通知主窗口重置多线程状态
                _mainWindow?.ResetMultiThreadState();
            }
        }

        /// <summary>
        /// 更新线程计数
        /// </summary>
        private void UpdateThreadCounts()
        {
            TotalThreadsText.Text = _threadViewModels.Count.ToString();
            
            var activeThreads = _threadViewModels.Count(t => 
                t.Status.Status == ThreadStatus.Processing || 
                t.Status.Status == ThreadStatus.WaitingForManualAction);
            
            ActiveThreadsText.Text = activeThreads.ToString();
        }

        /// <summary>
        /// 窗口关闭事件
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            // 取消事件订阅
            _multiThreadManager.ThreadStatusChanged -= OnThreadStatusChanged;
            _multiThreadManager.ThreadManualActionRequired -= OnThreadManualActionRequired;
            _multiThreadManager.ThreadDataCompleted -= OnThreadDataCompleted;
            _multiThreadManager.ThreadDataStarted -= OnThreadDataStarted;
            _multiThreadManager.OverallStatusChanged -= OnOverallStatusChanged;
            _multiThreadManager.ThreadSaveClipboardInfo -= OnThreadSaveClipboardInfo;
            _multiThreadManager.ThreadSaveFailedData -= OnThreadSaveFailedData;
            _multiThreadManager.ThreadPage2Completed -= OnThreadPage2Completed;
            
            base.OnClosed(e);
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        private void MultiThreadWindow_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // 恢复主窗口状态
                if (_mainWindow != null)
                {
                    _mainWindow.WindowState = WindowState.Normal;
                    _mainWindow.Activate();

                    // 清理主窗口中的多线程窗口引用
                    _mainWindow.ClearMultiThreadWindowReference();

                    _logService.LogInfo("主窗口已恢复正常状态，多线程窗口引用已清理");
                }

                _logService.LogInfo("多线程管理窗口正在关闭");
            }
            catch (Exception ex)
            {
                _logService.LogError($"关闭多线程管理窗口时出错: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 线程视图模型
    /// </summary>
    public class ThreadViewModel : INotifyPropertyChanged
    {
        public int ThreadId { get; set; }

        private string _currentEmail = string.Empty;
        public string CurrentEmail
        {
            get => _currentEmail;
            set
            {
                _currentEmail = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(ThreadTitle));
            }
        }

        public string ThreadTitle
        {
            get
            {
                if (string.IsNullOrEmpty(CurrentEmail))
                {
                    return $"🔧 线程 {ThreadId}";
                }
                return $"🔧 线程 {ThreadId} ({CurrentEmail})";
            }
        }

        private ThreadStatusInfo _status = new ThreadStatusInfo();
        public ThreadStatusInfo Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
                UpdateButtonStates();
            }
        }

        private bool _isPauseEnabled = true;
        public bool IsPauseEnabled
        {
            get => _isPauseEnabled;
            set
            {
                _isPauseEnabled = value;
                OnPropertyChanged();
            }
        }

        private bool _isContinueEnabled = false;
        public bool IsContinueEnabled
        {
            get => _isContinueEnabled;
            set
            {
                _isContinueEnabled = value;
                OnPropertyChanged();
            }
        }

        private bool _isTerminateEnabled = true;
        public bool IsTerminateEnabled
        {
            get => _isTerminateEnabled;
            set
            {
                _isTerminateEnabled = value;
                OnPropertyChanged();
            }
        }

        private void UpdateButtonStates()
        {
            switch (Status.Status)
            {
                case ThreadStatus.Processing:
                    IsPauseEnabled = true;
                    IsContinueEnabled = false;
                    IsTerminateEnabled = true;
                    break;

                case ThreadStatus.Paused:
                    IsPauseEnabled = false;
                    IsContinueEnabled = true;
                    IsTerminateEnabled = true;
                    break;

                case ThreadStatus.WaitingForManualAction:
                    IsPauseEnabled = true;
                    IsContinueEnabled = true; // 手动模式下可以继续
                    IsTerminateEnabled = true;
                    break;

                case ThreadStatus.Failed:
                    // 失败状态下，允许暂停和继续（用于重试），终止按钮始终可用
                    IsPauseEnabled = true;
                    IsContinueEnabled = true;
                    IsTerminateEnabled = true;
                    break;

                case ThreadStatus.Completed:
                case ThreadStatus.CompletedWithIssues:
                case ThreadStatus.Terminated:
                    IsPauseEnabled = false;
                    IsContinueEnabled = false;
                    IsTerminateEnabled = false;
                    break;

                default:
                    IsPauseEnabled = false;
                    IsContinueEnabled = false;
                    IsTerminateEnabled = true; // 终止按钮在任何情况下都可用
                    break;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
